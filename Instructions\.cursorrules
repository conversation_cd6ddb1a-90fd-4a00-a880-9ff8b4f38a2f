# Cursor AI Rules and Guidelines

## General Rules
- Always read and understand all documentation files before starting implementation
- Follow the PRD step by step
- Keep the project structure organized
- Update status.md after completing each step
- Ask for clarification when requirements are unclear

## File Purposes
- requirements.md: Source of truth for project requirements
- prd.md: Product specification and features
- techstack.md: Technical decisions and architecture
- backend.md: Backend implementation guide
- frontend.md: Frontend implementation guide
- flow.md: System and user flow documentation
- status.md: Progress tracking and milestones

## Best Practices
- Maintain consistent code style
- Write clear comments and documentation
- Follow the defined architecture
- Test thoroughly before marking tasks complete
- Keep the status.md file updated
