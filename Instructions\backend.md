```markdown
# Professional Web Trading Terminal - Backend Implementation Guide

**Version: 1.0**
**Date: June 29, 2025**

## 1. Document Header

*(Included above)*

## 2. API Design

The backend will expose APIs via both REST and WebSocket protocols. REST will be used for standard request/response operations (authentication, configuration, history), while WebSocket will be used for real-time data streaming and critical low-latency actions like order placement and updates.

**Base URLs:**
*   REST: `/api/v1`
*   WebSocket: `/ws/v1`

**Authentication:** JWT will be used for authentication on both REST (via `Authorization: Bearer <token>`) and WebSocket (sent as the first message or query parameter).

### 2.1 REST API Endpoints

*   **Authentication & User:**
    *   `POST /auth/login`: User login.
        *   Request Body: `{ username: string, password: string }`
        *   Response Body: `{ token: string, user: { id: string, username: string, ... } }`
    *   `POST /auth/register`: User registration.
        *   Request Body: `{ username: string, password: string, email: string }`
        *   Response Body: `{ message: string }` (or token on successful registration)
    *   `GET /user/profile`: Get authenticated user profile.
        *   Response Body: `{ id: string, username: string, email: string, settings: {...}, ... }`
    *   `PUT /user/profile`: Update authenticated user profile/settings.
        *   Request Body: `{ settings?: {...}, ... }`
        *   Response Body: `{ message: string, user: {...} }`
    *   `GET /account/balance`: Get account balance details.
        *   Response Body: `{ totalEquity: number, marginBalance: number, availableBalance: number, walletBalance: number, unrealizedPnl: number, ... }`

*   **Market Data (Historical/Static):**
    *   `GET /market/symbols`: Get list of supported trading symbols.
        *   Response Body: `[{ symbol: string, baseAsset: string, quoteAsset: string, precision: {...}, ... }]`
    *   `GET /market/ohlcv/{symbol}`: Get historical OHLCV data for a symbol.
        *   Query Params: `interval: string`, `startTime?: number` (timestamp), `endTime?: number` (timestamp), `limit?: number`
        *   Response Body: `[{ timestamp: number, open: string, high: string, low: string, close: string, volume: string }]`
    *   `GET /market/stats/{symbol}`: Get 24h market statistics.
        *   Response Body: `{ priceChange: string, priceChangePercent: string, highPrice: string, lowPrice: string, volume: string, quoteVolume: string, ... }`
    *   `GET /market/funding-rates/{symbol}`: Get historical funding rates.
        *   Query Params: `limit?: number`
        *   Response Body: `[{ timestamp: number, fundingRate: string, predictedFundingRate: string, nextFundingTime: number }]`

*   **Order & Position History:**
    *   `GET /order/history`: Get user's order history.
        *   Query Params: `symbol?: string`, `status?: string`, `startTime?: number`, `endTime?: number`, `limit?: number`
        *   Response Body: `[{ id: string, symbol: string, type: string, side: string, price: string, quantity: string, status: string, filledQuantity: string, executionTime: number, ... }]`
    *   `GET /trade/history`: Get user's trade history.
        *   Query Params: `symbol?: string`, `startTime?: number`, `endTime?: number`, `limit?: number`
        *   Response Body: `[{ id: string, symbol: string, orderId: string, price: string, quantity: string, commission: string, commissionAsset: string, executionTime: number, ... }]`
    *   `GET /position/history`: Get user's closed position history.
        *   Query Params: `symbol?: string`, `startTime?: number`, `endTime?: number`, `limit?: number`
        *   Response Body: `[{ id: string, symbol: string, entryPrice: string, exitPrice: string, realizedPnl: string, openTime: number, closeTime: number, ... }]`

*   **Admin (Requires Admin Role):**
    *   `GET /admin/users`: List all users.
    *   `GET /admin/user/{userId}`: Get user details.
    *   `PUT /admin/user/{userId}`: Update user details.
    *   `GET /admin/system/status`: Get system health/status.
    *   `GET /admin/trading/overview`: Get aggregate trading statistics.
    *   ... (More endpoints for contests, etc.)

### 2.2 WebSocket API Messages

Messages will typically follow a structure like `{ type: string, payload: any }` or `{ method: string, params: any, id?: number }` (like JSON-RPC). We'll use a topic-based pub/sub model.

*   **Connection:**
    *   Client sends auth token: `{ type: 'auth', payload: { token: string } }`
    *   Server responds: `{ type: 'authStatus', payload: { success: boolean, message?: string } }`

*   **Market Data Subscriptions:**
    *   Client subscribes: `{ type: 'subscribe', payload: { streams: string[] } }`
        *   Streams examples: `marketData.ohlcv.<symbol>.<interval>`, `marketData.book.<symbol>`, `marketData.trades.<symbol>`, `marketData.stats.<symbol>`, `marketData.funding.<symbol>`
    *   Client unsubscribes: `{ type: 'unsubscribe', payload: { streams: string[] } }`
    *   Server sends data: `{ type: 'marketDataUpdate', payload: { stream: string, data: any } }` (Data structure depends on the stream)
        *   Example OHLCV update: `{ type: 'marketDataUpdate', payload: { stream: 'marketData.ohlcv.BTCUSDT.1m', data: { kline: {...}, eventTime: number } } }`
        *   Example Order Book update: `{ type: 'marketDataUpdate', payload: { stream: 'marketData.book.BTCUSDT', data: { bids: [[price, quantity], ...], asks: [[price, quantity], ...], updateId: number } } }`
        *   Example Trade update: `{ type: 'marketDataUpdate', payload: { stream: 'marketData.trades.BTCUSDT', data: { symbol: string, price: string, quantity: string, time: number, buyerMaker: boolean, ... } } }`

*   **User Account Subscriptions (Authenticated):**
    *   Client subscribes: `{ type: 'subscribe', payload: { streams: string[], token: string } }`
        *   Streams examples: `account.balance`, `account.orders`, `account.positions`
    *   Server sends data: `{ type: 'accountUpdate', payload: { stream: string, data: any } }`
        *   Example Balance update: `{ type: 'accountUpdate', payload: { stream: 'account.balance', data: { totalEquity: number, ... } } }`
        *   Example Order update: `{ type: 'accountUpdate', payload: { stream: 'account.orders', data: { orderId: string, status: string, filledQuantity: string, ... } } }`
        *   Example Position update: `{ type: 'accountUpdate', payload: { stream: 'account.positions', data: { symbol: string, size: string, entryPrice: string, unrealizedPnl: string, ... } } }`

*   **Trading Actions (Authenticated - Request/Response over WS for low latency):**
    *   Client places order: `{ type: 'request', payload: { method: 'placeOrder', params: { symbol: string, type: string, side: string, quantity: string, price?: string, ... } }, id: 123 }`
    *   Server responds (confirmation/error): `{ type: 'response', payload: { success: boolean, orderId?: string, errorCode?: number, errorMessage?: string, ... }, id: 123 }`
    *   Client cancels order: `{ type: 'request', payload: { method: 'cancelOrder', params: { orderId: string, symbol: string } }, id: 124 }`
    *   Server responds: `{ type: 'response', payload: { success: boolean, orderId?: string, errorCode?: number, errorMessage?: string, ... }, id: 124 }`
    *   Client modifies order: `{ type: 'request', payload: { method: 'modifyOrder', params: { orderId: string, symbol: string, quantity?: string, price?: string, ... } }, id: 125 }`
    *   Server responds: `{ type: 'response', payload: { success: boolean, orderId?: string, errorCode?: number, errorMessage?: string, ... }, id: 125 }`

## 3. Data Models

A relational database (e.g., PostgreSQL, MySQL) is suitable for structured user, account, order, and position data, while time-series data (OHLCV, Trades) might benefit from a time-series database (e.g., InfluxDB, TimescaleDB) or optimized schema in a relational DB. Market data snapshots (Order Book) might be stored temporarily in a fast cache (Redis).

**User Table:**
*   `id` (UUID/PK)
*   `username` (VARCHAR, UNIQUE, NOT NULL)
*   `email` (VARCHAR, UNIQUE, NOT NULL)
*   `password_hash` (VARCHAR, NOT NULL)
*   `created_at` (TIMESTAMP, NOT NULL)
*   `updated_at` (TIMESTAMP, NOT NULL)
*   `role` (ENUM/VARCHAR, e.g., 'user', 'admin', NOT NULL)
*   `settings` (JSON/JSONB - for user preferences like theme, layout config)

**Account Table:**
*   `id` (UUID/PK)
*   `user_id` (UUID/FK to User, UNIQUE, NOT NULL)
*   `currency` (VARCHAR, e.g., 'USDT', NOT NULL)
*   `wallet_balance` (DECIMAL, NOT NULL, DEFAULT 0.0)
*   `unrealized_pnl` (DECIMAL, NOT NULL, DEFAULT 0.0)
*   `margin_balance` (DECIMAL - derived, can be calculated or stored)
*   `equity` (DECIMAL - derived)
*   `created_at` (TIMESTAMP, NOT NULL)
*   `updated_at` (TIMESTAMP, NOT NULL)

**Order Table:**
*   `id` (UUID/PK)
*   `exchange_order_id` (VARCHAR/BIGINT - ID assigned by the exchange)
*   `user_id` (UUID/FK to User, NOT NULL)
*   `account_id` (UUID/FK to Account, NOT NULL)
*   `symbol` (VARCHAR, NOT NULL)
*   `type` (ENUM/VARCHAR, e.g., 'LIMIT', 'MARKET', 'STOP_LOSS', NOT NULL)
*   `side` (ENUM/VARCHAR, e.g., 'BUY', 'SELL', NOT NULL)
*   `status` (ENUM/VARCHAR, e.g., 'NEW', 'FILLED', 'PARTIALLY_FILLED', 'CANCELED', 'REJECTED', NOT NULL)
*   `price` (DECIMAL - for limit/stop orders)
*   `quantity` (DECIMAL - requested quantity, NOT NULL)
*   `filled_quantity` (DECIMAL, NOT NULL, DEFAULT 0.0)
*   `average_fill_price` (DECIMAL)
*   `client_order_id` (VARCHAR - optional, client-generated ID for tracking)
*   `created_at` (TIMESTAMP, NOT NULL)
*   `updated_at` (TIMESTAMP, NOT NULL)
*   `exchange_response` (JSON/JSONB - raw response from exchange for debugging)
*   `stop_price` (DECIMAL - for stop orders)
*   `take_profit_price` (DECIMAL - for TP/Bracket orders)
*   `trailing_delta` (DECIMAL - for Trailing Stop orders)
*   `parent_order_id` (UUID/FK to Order - for OCO/Bracket)

**Position Table:**
*   `id` (UUID/PK)
*   `user_id` (UUID/FK to User, NOT NULL)
*   `account_id` (UUID/FK to Account, NOT NULL)
*   `symbol` (VARCHAR, NOT NULL)
*   `side` (ENUM/VARCHAR, 'LONG', 'SHORT', NOT NULL)
*   `size` (DECIMAL, NOT NULL)
*   `entry_price` (DECIMAL, NOT NULL)
*   `margin_type` (ENUM/VARCHAR, 'ISOLATED', 'CROSS')
*   `leverage` (DECIMAL)
*   `liquidation_price` (DECIMAL)
*   `unrealized_pnl` (DECIMAL, NOT NULL, DEFAULT 0.0)
*   `realized_pnl` (DECIMAL, NOT NULL, DEFAULT 0.0)
*   `open_time` (TIMESTAMP, NOT NULL)
*   `updated_at` (TIMESTAMP, NOT NULL)
*   `status` (ENUM/VARCHAR, 'OPEN', 'CLOSED')

**Trade Table:** (Records individual fills/executions)
*   `id` (UUID/PK)
*   `exchange_trade_id` (VARCHAR/BIGINT - ID assigned by the exchange)
*   `user_id` (UUID/FK to User, NOT NULL)
*   `account_id` (UUID/FK to Account, NOT NULL)
*   `order_id` (UUID/FK to Order, NOT NULL)
*   `symbol` (VARCHAR, NOT NULL)
*   `price` (DECIMAL, NOT NULL)
*   `quantity` (DECIMAL, NOT NULL)
*   `commission` (DECIMAL)
*   `commission_asset` (VARCHAR)
*   `execution_time` (TIMESTAMP, NOT NULL)
*   `buyer_maker` (BOOLEAN)

**Market Data (Time-Series/Cache):**
*   **OHLCV:** `symbol`, `interval`, `timestamp`, `open`, `high`, `low`, `close`, `volume` (store historical, potentially cache recent in Redis)
*   **Trades:** `symbol`, `timestamp`, `price`, `quantity`, `buyer_maker`, `trade_id` (store recent history, stream live)
*   **Order Book:** `symbol`, `timestamp`, `bids` (JSON/Array), `asks` (JSON/Array) (primarily cache in Redis for fast access, maybe snapshot history less frequently)

**Indices:**
*   `User`: `(username)`, `(email)`
*   `Account`: `(user_id)`
*   `Order`: `(user_id)`, `(account_id)`, `(symbol)`, `(status)`, `(created_at)`, `(exchange_order_id)`
*   `Position`: `(user_id)`, `(account_id)`, `(symbol)`, `(status)`
*   `Trade`: `(user_id)`, `(account_id)`, `(order_id)`, `(symbol)`, `(execution_time)`
*   `OHLCV`: `(symbol, interval, timestamp)` - composite index for time-series queries.

## 4. Business Logic

The core backend business logic revolves around:

1.  **Market Data Ingestion & Distribution:**
    *   Connecting to external exchange WebSocket APIs (e.g., Binance).
    *   Parsing, validating, and normalizing incoming market data streams (trades, order book, klines, stats, funding rates).
    *   Aggregating raw trade data into OHLCV bars if not provided directly by the source.
    *   Maintaining a live, in-memory representation of the Order Book and other critical data for each symbol.
    *   Storing historical data (OHLCV, Trades) efficiently in the database.
    *   Broadcasting real-time data updates to connected clients via WebSocket using a Pub/Sub pattern (e.g., Redis Pub/Sub, Kafka). Clients subscribe to specific data streams (`marketData.trades.BTCUSDT`, `marketData.book.ETHBTC`, etc.).

2.  **User & Account Management:**
    *   Handling user registration, login, and profile management (REST API).
    *   Generating and validating JWT tokens for authentication.
    *   Managing user account balances and equity.
    *   Processing deposits/withdrawals (if in scope, usually involves integration with payment gateways/blockchain).

3.  **Order Execution & Management:**
    *   Receiving order requests from authenticated clients (via low-latency WebSocket or REST).
    *   Validating order parameters (symbol, quantity, price, type, user balance, trading limits).
    *   Calculating margin impact and checking available balance/margin for new orders.
    *   Routing validated orders to the external exchange trading API (REST or their dedicated trading WS).
    *   Handling responses from the exchange (order confirmation, errors).
    *   Updating the internal `Order` and `Account` tables with the order status and details.
    *   Processing exchange execution reports (fills, partial fills, cancellations). This is critical and often comes via a dedicated exchange user data stream (WebSocket).
    *   Updating the internal `Order`, `Trade`, `Position`, and `Account` tables based on execution reports.
    *   Broadcasting real-time order status and fill updates to the specific user's connected clients via WebSocket (`account.orders` stream).
    *   Handling complex order types (Stop, TP, TS, OCO, Bracket) either by sending equivalent orders to the exchange or by managing trigger logic server-side if the exchange doesn't support them natively or if server-side precision/logic is required (e.g., trailing stops based on server's own price feed).

4.  **Position Tracking & Risk Management:**
    *   Maintaining the current state of all open positions for each user based on order fills.
    *   Calculating real-time Unrealized PnL for open positions based on the latest market price.
    *   Calculating margin usage, leverage, and liquidation price for each position and the overall account.
    *   Broadcasting real-time position updates and account balance changes to the user's clients (`account.positions`, `account.balance` streams).
    *   Implementing risk checks and alerts (e.g., margin call warnings) based on position and account metrics.

5.  **Admin Features:**
    *   Providing a secure interface (REST API) for admin users to monitor system health, user activity, trading volume, etc.
    *   Implementing user management functions (view, edit, disable users).
    *   Managing trading contests (if applicable) - setting up rules, tracking participation, calculating results.

## 5. Security

*   **Authentication:**
    *   Users authenticate via `/auth/login` (REST).
    *   Server issues a short-lived JWT containing user ID and role.
    *   Clients include the JWT in the `Authorization: Bearer <token>` header for subsequent REST requests.
    *   For WebSocket, the token is sent immediately after connection or included as a query parameter. The server validates the token to associate the connection with a user.
    *   Token validation middleware is applied to protected routes/WS connections.
    *   Implement token refresh mechanisms or use reasonably short token lifetimes with secure storage.

*   **Authorization:**
    *   Implement Role-Based Access Control (RBAC). JWT token or session state includes user roles (e.g., 'user', 'admin').
    *   Middleware/decorators check user roles and permissions before allowing access to API endpoints or sensitive WebSocket actions/subscriptions.
    *   Users can only access their own account data, orders, positions, etc. Requests attempting to access data belonging to another user must be rejected (using the user ID from the authenticated context). Admin roles have elevated privileges.

*   **Communication Security:**
    *   All communication must use encrypted channels: HTTPS for REST, WSS for WebSocket.

*   **Input Validation:**
    *   Strict validation, sanitization, and type-checking on *all* incoming data from clients (REST request bodies/query params, WebSocket message payloads).
    *   Reject requests with invalid or malicious data immediately (e.g., SQL injection attempts, script tags, excessive values, incorrect data types).

*   **Rate Limiting:**
    *   Apply rate limits on public endpoints (login, registration) and authenticated trading endpoints to prevent abuse and DoS attacks.
    *   Rate limits can be IP-based or user-based.

*   **Secure Coding Practices:**
    *   Use parameterized queries or ORM to prevent SQL injection.
    *   Properly handle errors and avoid leaking sensitive information in error responses.
    *   Sanitize outputs to prevent XSS (though primarily a frontend concern, backend APIs should not return unsanitized user input).
    *   Log security events (failed logins, access denied).

*   **Secrets Management:**
    *   Store API keys (exchange keys), database credentials, JWT secrets, etc., securely using environment variables, dedicated secrets management systems (HashiCorp Vault, AWS Secrets Manager), or encrypted configuration files. Never commit secrets to source code.

## 6. Performance

Achieving sub-100ms latency and handling 10,000+ concurrent users requires careful design and optimization:

*   **Real-time Data Pipeline:**
    *   Use an efficient, non-blocking WebSocket server implementation.
    *   Employ a robust Pub/Sub system (Redis, Kafka) to distribute market data updates internally.
    *   Process market data streams asynchronously and in separate processes/services if needed (e.g., one service ingests, another processes/aggregates, others broadcast).
    *   Minimize processing per message in the hot path for data broadcast. Filter data per client subscription *after* central processing if possible.
    *   Utilize fast in-memory stores (Redis) for current Order Book state, recent trades, and market stats to serve WebSocket clients quickly without hitting the database.

*   **Low-Latency Trading:**
    *   Trading actions (place, cancel, modify order) must be processed with minimal delay.
    *   Use a dedicated message queue or a highly performant internal bus for communication between the API gateway, order validation logic, and the exchange integration service.
    *   Interact with the exchange API using their most performant interface (often a dedicated trading WebSocket or highly optimized REST endpoints).
    *   Update internal state (Order, Position, Account) asynchronously after the exchange confirms the action, but immediately send a preliminary response to the client based on validation and submission success. Real-time updates via WS will provide the final status.

*   **Database Optimization:**
    *   Implement appropriate database indexing on frequently queried columns (user_id, symbol, status, timestamps).
    *   Optimize database queries to avoid full table scans, especially for historical data.
    *   Consider database replication (read replicas) to scale read operations, which will be frequent for history and dashboard data. Sharding might be necessary at very high scale.
    *   Use a Time-Series Database for OHLCV/Trades if volume is extremely high, or use PostgreSQL with TimescaleDB extension.

*   **Caching:**
    *   Cache frequently accessed static/slow-changing data (symbol list, user settings) using Redis or an in-memory cache.
    *   Cache real-time market data snapshots (Order Book, latest OHLCV).
    *   Cache user session/authentication data (JWT validity checks).

*   **Backend Architecture:**
    *   Design services/modules to be stateless where possible to facilitate horizontal scaling.
    *   Use a load balancer to distribute traffic across multiple instances of backend services.
    *   Monitor latency and resource usage constantly using APM tools. Identify bottlenecks and optimize specific code paths.
    *   Employ efficient data serialization formats (e.g., Protocol Buffers over JSON for internal communication if absolute speed is needed, although JSON is usually sufficient).

*   **Efficient Processing:**
    *   Use asynchronous programming models (Node.js event loop, async/await, Go routines, etc.) to handle multiple connections and requests concurrently without blocking.
    *   Minimize unnecessary data transfers and computations.
    *   Perform heavy calculations (like complex indicator calculations if server-side, position sizing) efficiently, perhaps offloading them to dedicated worker processes if they are blocking.

## 7. Code Examples

These examples are simplified and use a hypothetical Node.js/Express-like structure with external service calls or database interactions abstracted.

### 7.1 WebSocket Market Data Processing and Broadcasting

```typescript
// Using 'ws' library and a hypothetical Redis Pub/Sub for internal communication

import WebSocket from 'ws';
import Redis from 'ioredis';
import { parseMarketDataMessage, aggregateKlineData } from './marketDataProcessor'; // Helper functions
import { logger } from './logger';

// Assuming configuration loaded elsewhere
const EXTERNAL_EXCHANGE_WS_URL = 'wss://exchange.api/ws';
const REDIS_URL = 'redis://localhost:6379'; // For internal pub/sub

const redisPublisher = new Redis(REDIS_URL);
const redisSubscriber = new Redis(REDIS_URL); // Need a separate subscriber client

// --- Connect to external exchange WebSocket ---
const externalWS = new WebSocket(EXTERNAL_EXCHANGE_WS_URL);

externalWS.on('open', () => {
    logger.info('Connected to external exchange WebSocket.');
    // Subscribe to necessary raw streams from the exchange
    externalWS.send(JSON.stringify({
        method: 'SUBSCRIBE',
        params: [
            'btcusdt@kline_1m',
            'btcusdt@depth',
            'btcusdt@aggTrade',
            // Add more streams as needed
        ],
        id: 1
    }));
});

externalWS.on('message', async (data: WebSocket.Data) => {
    try {
        const message = JSON.parse(data.toString());
        logger.debug('Received raw market data:', message);

        // Process the raw message (normalize, aggregate, etc.)
        const processedData = parseMarketDataMessage(message); // { stream: string, data: any }

        if (processedData) {
            // Store historical data asynchronously if needed (e.g., OHLCV)
            if (processedData.stream.startsWith('marketData.ohlcv')) {
                 // Example: Persist kline data - should be non-blocking
                 // saveKlineToDB(processedData.data).catch(err => logger.error('Failed to save kline:', err));
            }

             // Cache frequently accessed data (e.g., Order Book snapshot)
             if (processedData.stream.startsWith('marketData.book')) {
                 // cacheOrderBook(processedData.data.symbol, processedData.data);
             }

            // Publish processed data to internal Pub/Sub channel
            // Clients subscribed via our WebSocket server will listen to these channels
            await redisPublisher.publish(processedData.stream, JSON.stringify(processedData.data));
            logger.debug(`Published data to ${processedData.stream}`);
        }

    } catch (error) {
        logger.error('Error processing market data message:', error);
    }
});

externalWS.on('error', (error: Error) => {
    logger.error('External exchange WebSocket error:', error);
    // Implement reconnection logic
});

externalWS.on('close', (code: number, reason: string) => {
    logger.warn(`External exchange WebSocket closed: ${code} - ${reason}`);
    // Implement reconnection logic
});

// --- WebSocket Server for Clients ---
// This would run in potentially multiple instances, subscribing to Redis Pub/Sub

import http from 'http';
import url from 'url';
import { v4 as uuidv4 } from 'uuid';
import { validateJwtToken } from './authService'; // Auth helper

const WS_PORT = process.env.WS_PORT || 8080;
const server = http.createServer();
const wss = new WebSocket.Server({ server });

// Map to store client subscriptions: clientId -> Set<streamName>
const clientSubscriptions = new Map<string, Set<string>>();
// Map to store WebSocket connection: clientId -> WebSocket
const clients = new Map<string, WebSocket>();
// Map to store client ID by WS connection instance (for easy lookup on message/close)
const wsToClientId = new Map<WebSocket, string>();
// Map to store user ID by client ID (for authenticated streams)
const clientToUserId = new Map<string, string>();

wss.on('connection', async (ws: WebSocket, req: http.IncomingMessage) => {
    const clientId = uuidv4();
    clients.set(clientId, ws);
    wsToClientId.set(ws, clientId);
    clientSubscriptions.set(clientId, new Set());

    logger.info(`Client connected: ${clientId}`);

    // Handle initial authentication message
    ws.once('message', async (message: WebSocket.Data) => {
        try {
            const authMessage = JSON.parse(message.toString());
            if (authMessage.type === 'auth' && authMessage.payload && authMessage.payload.token) {
                const token = authMessage.payload.token;
                const userId = await validateJwtToken(token); // Your auth service
                if (userId) {
                    clientToUserId.set(clientId, userId);
                    ws.send(JSON.stringify({ type: 'authStatus', payload: { success: true, message: 'Authenticated' } }));
                    logger.info(`Client ${clientId} authenticated as user ${userId}`);
                    // Auto-subscribe to user-specific streams after auth if needed,
                    // or wait for explicit 'subscribe' message.
                    // For now, we expect explicit subscribe.
                } else {
                    ws.send(JSON.stringify({ type: 'authStatus', payload: { success: false, message: 'Authentication failed' } }));
                    logger.warn(`Client ${clientId} authentication failed.`);
                    ws.close(1008, 'Authentication failed'); // 1008: Policy Violation
                }
            } else {
                 // If first message isn't auth, decide policy: reject or allow only public streams
                 // For a trading terminal, require auth for most interactions.
                 // Allow unauthenticated subscriptions for public market data.
                 ws.send(JSON.stringify({ type: 'authStatus', payload: { success: false, message: 'Authentication required first message' } }));
                 // ws.close(1008, 'Authentication required'); // Might close here, or handle public streams
                 logger.warn(`Client ${clientId} sent non-auth first message.`);
                 // Continue processing messages, but only allow public streams for this client
            }
        } catch (error) {
            logger.error(`Error during client ${clientId} authentication:`, error);
            ws.send(JSON.stringify({ type: 'authStatus', payload: { success: false, message: 'Authentication error' } }));
            ws.close(1011, 'Internal Server Error'); // 1011: Internal Error
        }
    });


    ws.on('message', async (message: WebSocket.Data) => {
        try {
            const request = JSON.parse(message.toString());
            logger.debug(`Client ${clientId} received message:`, request);

            // Check if client is authenticated if requesting authenticated streams/actions
            const userId = clientToUserId.get(clientId);
            const isAuthenticatedRequest = ['subscribe', 'request'].includes(request.type) &&
                                           (request.payload?.streams?.some((s: string) => s.startsWith('account.')) || request.payload?.method);

            if (isAuthenticatedRequest && !userId) {
                ws.send(JSON.stringify({ type: 'error', payload: { message: 'Authentication required for this action.' } }));
                logger.warn(`Unauthenticated client ${clientId} attempted authenticated action.`);
                return;
            }


            switch (request.type) {
                case 'subscribe':
                    if (request.payload && Array.isArray(request.payload.streams)) {
                        request.payload.streams.forEach((stream: string) => {
                            // Validate stream name format/existence
                            if (stream.startsWith('account.') && !userId) {
                                // Ignore account stream subscription if not authenticated
                                logger.warn(`Client ${clientId} (unauthenticated) tried to subscribe to account stream: ${stream}`);
                                return;
                            }
                            clientSubscriptions.get(clientId)?.add(stream);
                             // Subscribe client's WS connection to this specific stream's Redis channel
                             // Note: Redis subscription is per client *instance*.
                             // We need one Redis subscriber connection per WS server instance
                             // that listens to *all* channels clients are subscribed to on that instance.
                             // A better pattern is Redis PMSG (pattern subscribe) like `marketData.*`
                             // and then filter messages for specific clients based on `clientSubscriptions`.
                             // Simple implementation: subscribe to all streams the instance *might* need.
                             // redisSubscriber.subscribe(stream); // This is incorrect for N clients on 1 WS instance
                        });
                        logger.info(`Client ${clientId} subscribed to: ${Array.from(clientSubscriptions.get(clientId)!)}`);
                        ws.send(JSON.stringify({ type: 'subscriptionStatus', payload: { success: true, message: 'Subscribed', streams: Array.from(clientSubscriptions.get(clientId)!)} }));
                    }
                    break;
                case 'unsubscribe':
                     if (request.payload && Array.isArray(request.payload.streams)) {
                        request.payload.streams.forEach((stream: string) => {
                            clientSubscriptions.get(clientId)?.delete(stream);
                            // redisSubscriber.unsubscribe(stream); // Again, requires careful management
                        });
                         logger.info(`Client ${clientId} unsubscribed from: ${request.payload.streams.join(', ')}`);
                         ws.send(JSON.stringify({ type: 'subscriptionStatus', payload: { success: true, message: 'Unsubscribed', streams: request.payload.streams } }));
                     }
                    break;
                case 'request':
                    // Handle trading actions or other specific requests
                    if (request.payload && request.payload.method) {
                        // Route this request to the appropriate business logic handler
                        handleClientRequest(clientId, userId!, request); // Assume userId is valid here due to check above
                    }
                    break;
                default:
                    ws.send(JSON.stringify({ type: 'error', payload: { message: 'Unknown message type' } }));
                    logger.warn(`Client ${clientId} sent unknown message type: ${request.type}`);
                    break;
            }
        } catch (error) {
            logger.error(`Error processing client ${clientId} message:`, error);
            ws.send(JSON.stringify({ type: 'error', payload: { message: 'Internal server error' } }));
        }
    });

    ws.on('close', () => {
        logger.info(`Client disconnected: ${clientId}`);
        // Clean up subscriptions and client maps
        clientSubscriptions.delete(clientId);
        clients.delete(clientId);
        wsToClientId.delete(ws);
        clientToUserId.delete(clientId);
        // Need to manage Redis unsubscribe logic carefully here too
    });

    ws.on('error', (error: Error) => {
        logger.error(`Client ${clientId} WebSocket error:`, error);
        // The 'close' event will typically follow
    });
});

// --- Redis Pub/Sub Listener for internal data broadcasts ---
// This subscriber receives messages from the market data processor (or other services)
// and forwards them to the appropriate connected clients.

// Subscribe to a pattern matching all market data streams
redisSubscriber.psubscribe('marketData.*', (err) => {
    if (err) {
        logger.error('Failed to psubscribe to marketData.*:', err);
    } else {
        logger.info('Redis psubscribed to marketData.*');
    }
});

// Subscribe to a pattern matching all account data streams
redisSubscriber.psubscribe('account.*', (err) => {
    if (err) {
        logger.error('Failed to psubscribe to account.*:', err);
    } else {
        logger.info('Redis psubscribed to account.*');
    }
});


redisSubscriber.on('pmessage', (pattern: string, channel: string, message: string) => {
    // `channel` is the actual stream name, e.g., 'marketData.trades.BTCUSDT'
    // `message` is the JSON string of the data payload

    const update = {
        type: channel.startsWith('marketData.') ? 'marketDataUpdate' : 'accountUpdate', // Determine type based on channel pattern
        payload: {
            stream: channel,
            data: JSON.parse(message)
        }
    };

    // Iterate over connected clients and send the message if they are subscribed to this channel
    clients.forEach((ws, clientId) => {
        const subscriptions = clientSubscriptions.get(clientId);
        const userId = clientToUserId.get(clientId);

        // For account streams, only send if authenticated and subscribing
        if (channel.startsWith('account.')) {
            // The data payload for account streams should ideally already be user-specific
            // but here we just check if the *client* is authenticated and subscribed.
            // The 'handleClientRequest' for trading actions ensures data is user-specific.
             if (userId && subscriptions?.has(channel)) {
                 // Further check if the account data in the payload is relevant to THIS userId
                 // depending on how the account data streams are structured.
                 // E.g., if one stream contains updates for *all* user orders, filter here.
                 // If streams are user-specific (e.g., 'account.orders.<userId>'), then just check subscription.
                 // Let's assume streams are NOT user-specific for simplicity here, and filter by userId.
                 // A better design might have user-specific channels or data payloads containing the userId.

                 // Simplistic check: Assuming the 'data' payload MIGHT contain a userId field
                 const dataPayload = update.payload.data;
                 if (dataPayload && (dataPayload.userId === userId || dataPayload.accountId === userId)) { // Adjust field name as per payload structure
                      ws.send(JSON.stringify(update)).catch(err => logger.warn(`Failed to send WS message to client ${clientId}: ${err.message}`));
                 } else {
                     // If account stream data doesn't match the user, don't send
                     logger.debug(`Skipping account update for client ${clientId} (User ${userId}) on channel ${channel}`);
                 }
             }
        } else { // Market Data streams are public
            if (subscriptions?.has(channel)) {
                ws.send(JSON.stringify(update)).catch(err => logger.warn(`Failed to send WS message to client ${clientId}: ${err.message}`));
            }
        }
    });
});

// --- Placeholder for client request handler (e.g., trading actions) ---
async function handleClientRequest(clientId: string, userId: string, request: any) {
    logger.info(`Handling request from client ${clientId} (User ${userId}):`, request);

    const { method, params, id } = request.payload; // Assuming JSON-RPC like request payload

    let responsePayload: any = { success: false, errorMessage: 'Unknown method' };

    try {
        switch (method) {
            case 'placeOrder':
                responsePayload = await processPlaceOrder(userId, params); // Business logic function
                break;
            case 'cancelOrder':
                 responsePayload = await processCancelOrder(userId, params); // Business logic function
                break;
             case 'modifyOrder':
                responsePayload = await processModifyOrder(userId, params); // Business logic function
                break;
            // Add other methods
            default:
                 // Handled by initial responsePayload
                break;
        }
    } catch (error: any) {
        logger.error(`Error processing method ${method} for user ${userId}:`, error);
        responsePayload = { success: false, errorMessage: error.message || 'Internal server error' };
    }

    // Send response back to the specific client that made the request
    const ws = clients.get(clientId);
    if (ws && ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({ type: 'response', payload: responsePayload, id: id }));
    }
}

// --- Placeholder Business Logic Functions ---
async function processPlaceOrder(userId: string, params: any): Promise<any> {
    // 1. Validate params (symbol, type, side, quantity, price, etc.)
    // 2. Retrieve user account and balance information
    // 3. Calculate margin impact / check available balance
    // 4. If valid, construct exchange API request
    // 5. Send request to Exchange Integration Service/API (could be another internal service or direct call)
    // 6. Store initial order state in DB (status=NEW/PENDING, link to user/account)
    // 7. Return initial response to client (e.g., success: true, orderId: internalId)
    //    Note: The final status (FILLED, REJECTED, etc.) will come later via exchange user data stream
    //    and be broadcast via the account.orders WebSocket stream.

    logger.info(`User ${userId} placing order:`, params);

    // Basic Validation Example (simplified)
    if (!params.symbol || !params.type || !params.side || !params.quantity) {
        throw new Error('Missing required order parameters');
    }
    if (isNaN(parseFloat(params.quantity)) || parseFloat(params.quantity) <= 0) {
         throw new Error('Invalid quantity');
    }
    // ... more validation based on order type, price precision, etc.

    // Placeholder: Interact with Exchange API service
    const exchangeResponse = await callExchangeApi('placeOrder', { userId, ...params }); // Call internal service or wrapper

    if (exchangeResponse.success) {
        // Store order in database
        const newOrder = {
            id: uuidv4(),
            exchange_order_id: exchangeResponse.exchangeOrderId, // Get from exchange response
            user_id: userId,
            account_id: '...', // Get user's account ID
            symbol: params.symbol,
            type: params.type,
            side: params.side,
            price: params.price,
            quantity: params.quantity,
            status: 'PENDING_NEW', // Or NEW, depending on workflow
            filled_quantity: 0,
            created_at: new Date(),
            updated_at: new Date(),
            // ... other fields
        };
        // await saveOrderToDB(newOrder); // Non-blocking DB save

        // TODO: Update account balance/margin state immediately based on order (e.g., reserving margin for limit orders)

        // TODO: Publish initial order state update via Redis Pub/Sub
        // redisPublisher.publish(`account.orders.${userId}`, JSON.stringify(newOrder)); // If channels are user-specific
        // Or if account stream is broadcast to all users (less likely for orders), payload needs userId

        return { success: true, orderId: newOrder.id, clientOrderId: params.clientOrderId, status: newOrder.status };

    } else {
        // Handle exchange-side errors
        logger.error(`Exchange rejected order for user ${userId}:`, exchangeResponse.errorMessage);
        // Optionally store failed order attempt
        return { success: false, errorMessage: exchangeResponse.errorMessage, errorCode: exchangeResponse.errorCode };
    }
}

async function processCancelOrder(userId: string, params: any): Promise<any> {
    // 1. Validate params (orderId or clientOrderId, symbol)
    // 2. Look up order in DB to get exchange_order_id
    // 3. Check if order is cancellable (status not FILLED/CANCELED/REJECTED) and belongs to user
    // 4. Send cancel request to Exchange Integration Service/API
    // 5. Update internal order status to PENDING_CANCEL
    // 6. Return initial response
    // 7. Exchange user data stream will confirm final status (CANCELED) which triggers WS broadcast.

    logger.info(`User ${userId} cancelling order:`, params);

    if (!params.orderId && !params.clientOrderId) {
        throw new Error('Order ID or Client Order ID is required');
    }
    if (!params.symbol) {
        throw new Error('Symbol is required for cancellation');
    }

    // Find the order in DB (e.g., by internal ID or client ID)
    const orderToCancel = await findOrderInDB(params.orderId, params.clientOrderId, userId);
    if (!orderToCancel) {
        throw new Error('Order not found or does not belong to user');
    }
    if (['FILLED', 'CANCELED', 'REJECTED'].includes(orderToCancel.status)) {
         throw new Error(`Order is already in status: ${orderToCancel.status}`);
    }

    // Placeholder: Interact with Exchange API service
    const exchangeResponse = await callExchangeApi('cancelOrder', {
        userId,
        symbol: orderToCancel.symbol,
        exchangeOrderId: orderToCancel.exchange_order_id,
        clientOrderId: orderToCancel.client_order_id // Use exchange's clientOrderId if needed
    });

     if (exchangeResponse.success) {
         // Update order status in DB to PENDING_CANCEL
         // await updateOrderStatusInDB(orderToCancel.id, 'PENDING_CANCEL');

         // Publish update (optional, official update comes from exchange stream)
         // redisPublisher.publish(`account.orders.${userId}`, JSON.stringify({...orderToCancel, status: 'PENDING_CANCEL'}));

         return { success: true, orderId: orderToCancel.id, status: 'PENDING_CANCEL' };

     } else {
         logger.error(`Exchange rejected cancel order for user ${userId}, order ${orderToCancel.id}:`, exchangeResponse.errorMessage);
         // It's possible the order was already filled/canceled on the exchange side.
         // The exchange user data stream will be the source of truth.
         return { success: false, errorMessage: exchangeResponse.errorMessage, errorCode: exchangeResponse.errorCode };
     }
}

async function processModifyOrder(userId: string, params: any): Promise<any> {
    // Similar flow to cancel, but send modify request to exchange.
    // Need to handle which fields can be modified (price, quantity for Limit orders usually).
    // Exchange user data stream confirms the modification or a new order ID.
     logger.info(`User ${userId} modifying order:`, params);
     // ... validation and exchange API call ...
      return { success: true, message: 'Modification request sent' }; // Placeholder
}


// --- Placeholder for interacting with Exchange API (could be another service) ---
async function callExchangeApi(method: string, params: any): Promise<any> {
    logger.info(`Calling exchange API method "${method}" with params:`, params);
    // This would involve:
    // - Selecting the correct exchange based on symbol (if multi-exchange)
    // - Retrieving API keys for the user
    // - Formatting the request according to the exchange's API (REST or WS)
    // - Sending the request and handling the response
    // - Implementing retries and error handling

    // Simulate a successful response after a small delay
    await new Promise(resolve => setTimeout(resolve, 50)); // Simulate network latency + exchange processing

    if (method === 'placeOrder') {
        // Simulate success response for placing order
        const exchangeOrderId = Math.random().toString(36).substring(7); // Dummy ID
        logger.info(`Simulated exchange placeOrder success, ID: ${exchangeOrderId}`);
        return { success: true, exchangeOrderId: exchangeOrderId, status: 'NEW' };
    } else if (method === 'cancelOrder') {
         logger.info(`Simulated exchange cancelOrder success`);
        return { success: true, status: 'CANCELED' };
    } else if (method === 'modifyOrder') {
         logger.info(`Simulated exchange modifyOrder success`);
        return { success: true, status: 'NEW' }; // Modify might return NEW status
    }


    return { success: false, errorMessage: 'Simulated Error: Unsupported exchange method' };
}

// --- Placeholder for database operations ---
async function saveKlineToDB(klineData: any) {
    logger.debug('Saving kline to DB (simulated):', klineData);
    // Your database INSERT/UPSERT logic here
}

async function saveOrderToDB(order: any) {
    logger.debug('Saving order to DB (simulated):', order);
     // Your database INSERT logic here
}

async function findOrderInDB(orderId: string | undefined, clientOrderId: string | undefined, userId: string): Promise<any | null> {
    logger.debug(`Finding order for user ${userId} by ID ${orderId} or Client ID ${clientOrderId}`);
    // Your database SELECT logic here, ensuring user_id matches
    // Return a dummy order object if found in simulation
    if (orderId === 'some-order-id' || clientOrderId === 'some-client-id') {
         return {
             id: orderId || 'some-order-id',
             client_order_id: clientOrderId || 'some-client-id',
             user_id: userId,
             symbol: 'BTCUSDT',
             status: 'NEW', // Or partially filled, etc.
             exchange_order_id: 'exchange-id-123',
             // ... other fields
         };
    }
    return null; // Not found
}

async function updateOrderStatusInDB(orderId: string, status: string) {
     logger.debug(`Updating order ${orderId} status to ${status} (simulated)`);
     // Your database UPDATE logic here
}


// Start the WebSocket server
server.listen(WS_PORT, () => {
    logger.info(`WebSocket server listening on port ${WS_PORT}`);
});

// Keep the Node.js process running
process.on('SIGINT', () => {
    logger.info('Shutting down...');
    externalWS.close();
    wss.close(() => {
        logger.info('WebSocket server closed.');
        redisPublisher.quit();
        redisSubscriber.quit();
        // Close DB connections etc.
        process.exit(0);
    });
});

// Note: In a real-world scenario, Redis subscribers often run on separate
// processes/instances from the Redis publisher and the WS server instances
// scale horizontally. The 'pmessage' listener logic would be in each WS server instance.
// The Exchange data ingestion might also be a dedicated service.

```

### 7.2 JWT Authentication Middleware (Example using Express)

```typescript
// Assuming you use Express or a similar framework

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { logger } from './logger'; // Your logging utility

// Load your JWT secret securely
const JWT_SECRET = process.env.JWT_SECRET || 'your_super_secret_jwt_key'; // CHANGE THIS IN PRODUCTION

// Extend Request type to include user info
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        role: string;
        // Add other essential user info from token
    };
}

export const authenticateJWT = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;

    if (authHeader) {
        const token = authHeader.split(' ')[1]; // Expects "Bearer TOKEN"

        jwt.verify(token, JWT_SECRET, (err, decodedToken) => {
            if (err) {
                // Token is invalid or expired
                logger.warn('JWT verification failed:', err.message);
                return res.sendStatus(403); // Forbidden (or 401 Unauthorized)
            }

            // Check if decoded token has expected structure and claims
            if (typeof decodedToken === 'object' && decodedToken !== null && 'id' in decodedToken && 'role' in decodedToken) {
                req.user = {
                    id: decodedToken.id,
                    role: decodedToken.role,
                    // Extract other fields from token payload if needed
                };
                logger.debug(`Authenticated user: ${req.user.id} with role ${req.user.role}`);
                next(); // Authentication successful, proceed to the next middleware/route handler
            } else {
                logger.warn('Invalid JWT token payload structure');
                 return res.sendStatus(403); // Forbidden
            }
        });
    } else {
        // No token provided
        logger.warn('No JWT token provided');
        res.sendStatus(401); // Unauthorized
    }
};

// Example usage in an Express route:
/*
import express from 'express';
const router = express.Router();

router.get('/user/profile', authenticateJWT, (req: AuthenticatedRequest, res) => {
    // If we reach here, req.user is populated
    const userId = req.user.id;
    logger.info(`Accessing profile for user ${userId}`);
    // Fetch user profile from DB using userId
    // ...
    res.json({ message: 'User profile data', user: req.user }); // Example response
});

// Add authorization check (e.g., for admin routes)
export const authorizeRole = (requiredRole: string) => {
    return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        if (!req.user) {
            // Should not happen if authenticateJWT runs first, but good defensive check
            return res.sendStatus(401); // Unauthorized
        }
        if (req.user.role !== requiredRole) {
            logger.warn(`User ${req.user.id} (role: ${req.user.role}) attempted to access restricted resource (requires: ${requiredRole})`);
            return res.sendStatus(403); // Forbidden
        }
        next(); // Authorized, proceed
    };
};

// Example admin route
router.get('/admin/users', authenticateJWT, authorizeRole('admin'), (req: AuthenticatedRequest, res) => {
    logger.info(`Admin user ${req.user.id} accessing user list`);
    // Fetch all users from DB
    // ...
    res.json({ message: 'List of all users', users: [] }); // Example
});
*/
```

```
```
