```markdown
# Frontend Implementation Guide: Professional Web Trading Terminal

**Version:** 1.0
**Date:** June 29, 2025

This guide provides technical direction and actionable steps for the frontend development of the Professional Web Trading Terminal, aligning with the project's goals for performance, real-time data, and a professional user experience.

---

## 1. Component Architecture

The frontend architecture will be modular and component-driven, primarily using React with TypeScript. The core structure will revolve around a main layout container managing multiple independent panels, each responsible for a specific part of the trading interface.

```mermaid
graph TD
    A[App Root] --> B(Layout Container);
    B --> C[Chart Panel];
    B --> D[Order Book Panel];
    B --> E[Recent Trades Panel];
    B --> F[Order Panel];
    B --> G[Positions Panel];
    B --> H[Order History Panel];

    C --> C1[TradingView LW Chart Component];
    C --> C2[Charting Tools/Indicators Controls];
    C1 --> C3[Interactive Order Placement/Modification Logic];

    D --> D1[Order Book Component];
    D1 --> D2[Virtualization Logic];

    E --> E1[Recent Trades Component];
    E1 --> E2[Virtualization Logic];

    F --> F1[Order Form Component];
    F --> F2[Position Sizing Calculator];
    F1 --> F3[Order Placement/Modification API Calls];

    G --> G1[Positions Table Component];
    G1 --> G2[Real-time P&L Display];
    G1 --> G3[Risk Metrics Display];
    G1 --> G4[Virtualization Logic];

    H --> H1[Order History Table Component];
    H1 --> H2[Virtualization Logic];

    B --> I[Header/Navigation];
    I --> I1[Symbol Selector];
    I --> I2[Account Info/Balance];
    I --> I3[Theme Toggle];

    % State and API interaction
    Style A fill:#f9f,stroke:#333,stroke-width:2px
    Style B fill:#ccf,stroke:#333,stroke-width:2px
    Style C fill:#cfc,stroke:#333,stroke-width:2px
    Style D fill:#cfc,stroke:#333,stroke-width:2px
    Style E fill:#cfc,stroke:#333,stroke-width:2px
    Style F fill:#cfc,stroke:#333,stroke-width:2px
    Style G fill:#cfc,stroke:#333,stroke-width:2px
    Style H fill:#cfc,stroke:#333,stroke-width:2px
    Style I fill:#ffc,stroke:#333,stroke-width:2px

    State[State Management (Zustand)] --> C, D, E, F, G, H, I;
    API[API Integration (REST/WS)] --> State; % API pushes data to state
    F3 --> API; % Order actions trigger API calls
    C3 --> API; % Interactive chart orders trigger API calls
```

**Core Components:**

*   **`App`:** Root component, sets up context, theme, routing (if needed).
*   **`LayoutContainer`:** Manages the overall grid/flex layout of panels. Could potentially use a library like `react-grid-layout` for resizable/draggable panels, though a fixed multi-panel grid is the baseline. Manages Dark/Light theme context.
*   **`Panel`:** A generic container component for each section (Chart, Order Book, etc.) with consistent styling, potentially including a title bar and symbol context.
*   **`ChartPanel`:** Contains the `TradingViewLightweightChart` component and controls for timeframes, indicators, etc. Handles interactive chart interactions.
*   **`OrderBookPanel`:** Displays real-time order book data, likely using virtualization for performance.
*   **`RecentTradesPanel`:** Displays real-time recent trades, also using virtualization.
*   **`OrderPanel`:** Contains forms for placing various order types (`Market`, `Limit`, `Stop`, etc.), position sizing calculator.
*   **`PositionsPanel`:** Displays active positions with real-time P&L and metrics, using virtualization.
*   **`OrderHistoryPanel`:** Displays historical orders, using virtualization and pagination/lazy loading if needed.
*   **`Header`:** Contains symbol selector, account summary, theme toggle, etc.
*   **UI Primitives:** Buttons, Inputs, Modals, Spinners, Tooltips, etc.

Each panel and its sub-components should be as decoupled as possible, primarily interacting via state management and explicit props/callbacks.

## 2. State Management

Zustand is the chosen state management library due to its simplicity, performance, and hook-based API. We will use multiple, focused Zustand stores rather than a single large store to maintain modularity and improve performance by limiting re-renders to components subscribed to specific state changes.

**Key Stores:**

1.  **`useMarketDataStore`:** Manages real-time market data per symbol.
    *   State: `ohlcvData`, `orderBook`, `recentTrades`, `marketStats`, `fundingRate`, `currentSymbol`, `timeframe`.
    *   Actions: `setOHLCV`, `updateOrderBook`, `addTrade`, `setMarketStats`, `setFundingRate`, `setSymbol`, `setTimeframe`. Actions will be triggered by WebSocket messages.
2.  **`useAccountStore`:** Manages user account state.
    *   State: `balance`, `positions`, `openOrders`, `orderHistory`, `loading`, `error`.
    *   Actions: `setBalance`, `addPosition`, `updatePosition`, `removePosition`, `addOrder`, `updateOrder`, `removeOrder`, `addOrderHistoryEntry`. Actions triggered by WebSocket and REST API.
3.  **`useUIStore`:** Manages global UI state not tied to specific data.
    *   State: `theme` ('dark' | 'light'), `isLoading` (global loading state), `error` (global error messages), `modalOpen` (for global modals).
    *   Actions: `setTheme`, `setLoading`, `setError`, `openModal`, `closeModal`.
4.  **`useChartStore`:** Manages state specific to the chart component (indicators, drawings).
    *   State: `activeIndicators`, `drawingObjects`.
    *   Actions: `addIndicator`, `removeIndicator`, `addDrawing`, `updateDrawing`, `removeDrawing`.

**Implementation Details:**

*   Use Immer middleware with Zustand for easier immutable state updates, especially important for complex nested data like the order book or OHLCV series.
*   Selectors: Define clear selectors (`useMarketDataStore(state => state.ohlcvData)`) to ensure components only re-render when the specific data they consume changes.
*   WebSocket integration will directly call Zustand actions to update relevant stores upon receiving messages.

```typescript
// Example: useMarketDataStore.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface OrderBookEntry { price: number; size: number; }
interface Trade { time: number; price: number; size: number; isBuyerMaker: boolean; }
interface OHLCV { time: number; open: number; high: number; low: number; close: number; volume: number; }

interface MarketDataState {
  currentSymbol: string | null;
  ohlcvData: OHLCV[];
  orderBook: { bids: OrderBookEntry[]; asks: OrderBookEntry[]; };
  recentTrades: Trade[];
  marketStats: any | null; // Define proper type
  fundingRate: number | null;
}

interface MarketDataActions {
  setSymbol: (symbol: string) => void;
  setOHLCV: (data: OHLCV[]) => void;
  updateOrderBook: (bids: OrderBookEntry[], asks: OrderBookEntry[]) => void;
  addTrade: (trade: Trade) => void;
  setMarketStats: (stats: any) => void;
  setFundingRate: (rate: number) => void;
  resetMarketData: () => void; // Helper to clear data on symbol change
}

export const useMarketDataStore = create<MarketDataState & MarketDataActions>()(
  immer((set) => ({
    currentSymbol: null,
    ohlcvData: [],
    orderBook: { bids: [], asks: [] },
    recentTrades: [],
    marketStats: null,
    fundingRate: null,

    setSymbol: (symbol) => set(state => { state.currentSymbol = symbol; state.resetMarketData(); }), // Reset on symbol change
    setOHLCV: (data) => set(state => { state.ohlcvData = data; }),
    updateOrderBook: (bids, asks) => set(state => { state.orderBook = { bids, asks }; }),
    addTrade: (trade) => set(state => {
        state.recentTrades.unshift(trade); // Add new trade to the beginning
        if (state.recentTrades.length > 50) { // Limit trades to avoid excessive memory
            state.recentTrades.pop();
        }
    }),
    setMarketStats: (stats) => set(state => { state.marketStats = stats; }),
    setFundingRate: (rate) => set(state => { state.fundingRate = rate; }),
    resetMarketData: () => set(state => {
      state.ohlcvData = [];
      state.orderBook = { bids: [], asks: [] };
      state.recentTrades = [];
      state.marketStats = null;
      state.fundingRate = null;
    }),
  }))
);
```

## 3. UI Design

Focus on a dense, information-rich layout optimized for desktop monitors while ensuring readability and usability.

*   **Layout:** Implement a responsive grid or flexbox layout that clearly separates the required panels. Use Tailwind CSS for utility-first styling, making it easy to define grid structures and control spacing. Consider `display: grid` for the main layout.
*   **Themes:** Implement Dark and Light themes using CSS variables or Tailwind CSS theme configuration. A simple `ThemeToggle` component will switch a class on the body or root element, applying the theme.
*   **Data Density & Readability:** Use clear typography, appropriate line heights, and visual hierarchy to present data efficiently. Color coding (e.g., red for asks/sells, green for bids/buys/buys, red for losing positions, green for winning) is essential.
*   **Performance:**
    *   **Virtualization:** Crucial for tables/lists with potentially thousands of rows (Order Book, Recent Trades, Positions, History). Use libraries like `react-window` or `react-virtualized`.
    *   **Memoization:** Apply `React.memo` to functional components that receive props that change frequently but whose rendering logic might not need to re-run if props are structurally equal (e.g., individual rows in a list, the chart component itself). Use `useMemo` and `useCallback` for expensive calculations or stable function references passed as props.
    *   **CSS:** Leverage native CSS performance features (e.g., `will-change`, hardware acceleration via `transform: translateZ(0)` where appropriate, but use sparingly).
*   **Interactive Elements:** Design clear and intuitive interactive elements, especially for chart trading (drag-and-drop orders/stops) and order form inputs. Provide immediate visual feedback on user actions (e.g., confirmation messages, order lines on chart).

```tsx
// Example: Basic Layout using Tailwind CSS Grid
import React from 'react';
import { ChartPanel } from './ChartPanel';
import { OrderBookPanel } from './OrderBookPanel';
import { RecentTradesPanel } from './RecentTradesPanel';
import { OrderPanel } from './OrderPanel';
import { PositionsPanel } from './PositionsPanel';
import { OrderHistoryPanel } from './OrderHistoryPanel';
import { Header } from './Header';

export const LayoutContainer: React.FC = () => {
  return (
    <div className="grid grid-cols-12 grid-rows-layout-md gap-4 p-4 h-screen bg-background text-foreground dark:bg-background-dark dark:text-foreground-dark">
      {/* Define custom grid rows in tailwind.config.js for different screen sizes */}
      <header className="col-span-12 row-span-1">
        <Header />
      </header>

      <main className="col-span-12 grid grid-cols-12 gap-4 row-span-11">
        {/* Main trading area - Example distribution */}
        <section className="col-span-9 row-span-8 overflow-hidden">
          <ChartPanel />
        </section>

        <aside className="col-span-3 row-span-8 grid grid-rows-2 gap-4 overflow-hidden">
            {/* Right side panels */}
            <div className="row-span-1 overflow-hidden"><OrderBookPanel /></div>
            <div className="row-span-1 overflow-hidden"><RecentTradesPanel /></div>
        </aside>

        <section className="col-span-12 row-span-3 grid grid-cols-12 gap-4 overflow-hidden">
            {/* Bottom panels */}
            <div className="col-span-3 overflow-hidden"><OrderPanel /></div>
            <div className="col-span-6 overflow-hidden"><PositionsPanel /></div>
            <div className="col-span-3 overflow-hidden"><OrderHistoryPanel /></div>
        </section>
      </main>
    </div>
  );
};
```

## 4. API Integration

Integration will involve both REST and WebSocket APIs provided by the backend. A dedicated service layer will abstract API calls away from components and state management logic.

*   **REST API:** Used for initial data fetches (e.g., historical OHLCV data, full order history on load, account details), placing certain order types, and possibly authentication.
    *   Use `fetch` or `axios` for making requests.
    *   Centralize API endpoint definitions and request logic in service files (e.g., `services/apiService.ts`, `services/authService.ts`).
    *   Include JWT token in the `Authorization` header for authenticated requests.
*   **WebSocket API:** Used for real-time market data updates (OHLCV, Order Book deltas, Trades, Stats, Funding Rates) and real-time user data updates (position changes, order fills/updates).
    *   Use the browser's native `WebSocket` API or a robust library if more advanced features (like automatic reconnection, multiplexing) are needed.
    *   Implement a `WebSocketManager` class or hook to manage the connection state, handle message parsing, and dispatch actions to the relevant Zustand stores.
    *   Handle reconnection logic with exponential backoff.
    *   Implement message routing based on message type (e.g., 'marketdata.ohlcv', 'account.position_update').
*   **Authentication:**
    *   On login, store the received JWT securely (e.g., in HttpOnly cookies or localStorage, with careful security considerations).
    *   Attach the token to all authenticated REST requests.
    *   Authenticate the WebSocket connection, possibly via a token passed during connection setup or as the first message.
*   **Error Handling:** Implement consistent error handling for both REST and WS. Display informative error messages to the user via UI state (e.g., `useUIStore`).

```typescript
// Example: WebSocket Manager Basic Structure
import { useMarketDataStore } from '../state/useMarketDataStore';
import { useAccountStore } from '../state/useAccountStore';
import { useUIStore } from '../state/useUIStore';

const WS_URL = 'wss://your-trading-api.com/ws';
let websocket: WebSocket | null = null;
let reconnectAttempts = 0;
const MAX_RECONNECT_ATTEMPTS = 10;
const RECONNECT_DELAY_MS = 1000; // Initial delay

export const connectWebSocket = (token: string) => {
  if (websocket && websocket.readyState === WebSocket.OPEN) {
    console.log('WebSocket already connected.');
    return;
  }

  websocket = new WebSocket(`${WS_URL}?token=${token}`); // Or pass token in handshake message

  websocket.onopen = () => {
    console.log('WebSocket connected.');
    reconnectAttempts = 0; // Reset attempts on successful connection
    useUIStore.getState().setError(null); // Clear potential connection error
    // Subscribe to streams based on current symbol/user needs
    const currentSymbol = useMarketDataStore.getState().currentSymbol;
    if (currentSymbol) {
      subscribeToMarketData(currentSymbol);
    }
    subscribeToAccountUpdates(); // Assuming a global account stream
  };

  websocket.onmessage = (event) => {
    try {
      const message = JSON.parse(event.data);
      handleWebSocketMessage(message);
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  };

  websocket.onerror = (error) => {
    console.error('WebSocket error:', error);
    useUIStore.getState().setError('WebSocket connection error. Attempting to reconnect...');
  };

  websocket.onclose = (event) => {
    console.warn('WebSocket closed:', event.code, event.reason);
    // Attempt reconnection
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      const delay = RECONNECT_DELAY_MS * Math.pow(2, reconnectAttempts);
      console.log(`Attempting reconnect ${reconnectAttempts + 1} in ${delay}ms...`);
      setTimeout(() => connectWebSocket(token), delay);
      reconnectAttempts++;
    } else {
      console.error('Max reconnect attempts reached. Connection failed.');
      useUIStore.getState().setError('Failed to connect to trading data. Please refresh.');
    }
  };
};

export const disconnectWebSocket = () => {
  if (websocket) {
    websocket.close();
    websocket = null;
  }
};

const handleWebSocketMessage = (message: any) => {
  // Example message routing (based on backend message structure)
  switch (message.type) {
    case 'marketdata.ohlcv_update':
      useMarketDataStore.getState().setOHLCV(message.data); // Assuming data is the full updated series
      break;
    case 'marketdata.orderbook_update':
      useMarketDataStore.getState().updateOrderBook(message.data.bids, message.data.asks);
      break;
    case 'marketdata.trade':
      useMarketDataStore.getState().addTrade(message.data);
      break;
    // ... handle other market data types
    case 'account.position_update':
      useAccountStore.getState().updatePosition(message.data); // Assuming data is the updated position
      break;
    case 'account.order_update':
      useAccountStore.getState().updateOrder(message.data); // Assuming data is the updated order
      break;
    // ... handle other account update types
    default:
      console.warn('Unknown WebSocket message type:', message.type);
  }
};

// Example subscription functions (backend specific)
const subscribeToMarketData = (symbol: string) => {
  if (websocket && websocket.readyState === WebSocket.OPEN) {
    websocket.send(JSON.stringify({ action: 'subscribe', stream: `marketdata.${symbol}` }));
  }
};

const subscribeToAccountUpdates = () => {
   if (websocket && websocket.readyState === WebSocket.OPEN) {
    websocket.send(JSON.stringify({ action: 'subscribe', stream: `account.updates` }));
  }
}

// Call connectWebSocket on app load or after successful authentication
// Call disconnectWebSocket on logout or app unload
```

## 5. Testing Approach

A multi-layered testing strategy is essential to ensure reliability, performance, and correctness in a real-time trading application.

*   **Unit Tests:**
    *   **Focus:** Individual components (render logic, props handling), Zustand actions and reducers, utility functions, API service functions (mocking network requests).
    *   **Tools:** Jest, React Testing Library (for components).
    *   **Goal:** Verify small, isolated units of code work as expected. Mock dependencies aggressively.
*   **Integration Tests:**
    *   **Focus:** Interaction between components, how components use Zustand stores, how API service calls are handled by components/state.
    *   **Tools:** React Testing Library, Jest.
    *   **Goal:** Verify that different units work together correctly. Mock API network calls, but test the logic that *uses* the API services and updates state. Test component trees and how they respond to state changes.
*   **End-to-End (E2E) Tests:**
    *   **Focus:** Critical user flows from login to order placement and position management, simulating real user interactions.
    *   **Tools:** Cypress or Playwright.
    *   **Goal:** Verify the application works correctly from the user's perspective against a deployed backend (staging environment preferred). Includes testing interactions with the chart, order forms, and panel updates.
*   **Performance Testing:**
    *   **Focus:** Measure render times, component update frequency, bundle size, initial load time, and real-time data throughput handling.
    *   **Tools:** React Profiler (DevTools), Lighthouse, WebPageTest, Browser Developer Tools (Performance tab, Network tab).
    *   **Goal:** Identify bottlenecks, ensure smooth rendering (60fps where possible), and verify performance targets (<100ms latency, <50ms data updates visible on UI) are met under simulated load. Test with a large number of data updates.
*   **Reliability Testing:**
    *   **Focus:** WebSocket connection stability, reconnection logic, error handling scenarios (API errors, invalid data, network interruptions).
    *   **Goal:** Ensure the application remains usable and recovers gracefully from expected and unexpected failures.
*   **Visual Regression Testing:**
    *   **Focus:** Catch unintended UI changes across development cycles.
    *   **Tools:** Storybook with an addon, Percy, Chromatic.
    *   **Goal:** Maintain visual consistency, especially important with complex layouts and theming.

Integrate tests into the CI/CD pipeline to ensure code quality is maintained continuously.

## 6. Code Examples

Below are basic examples illustrating key concepts:

**Example 1: Integrating TradingView Lightweight Charts & React**

```tsx
// ChartPanel.tsx - Simplified example
import React, { useEffect, useRef, useState, useMemo } from 'react';
import { createChart, IChartApi, ISeriesApi, CandlestickData } from 'lightweight-charts';
import { useMarketDataStore } from '../state/useMarketDataStore';
import { useUIStore } from '../state/useUIStore'; // Assuming UI store manages loading state

export const ChartPanel: React.FC = () => {
  const { ohlcvData, currentSymbol, timeframe } = useMarketDataStore();
  const { isLoading, setLoading } = useUIStore(); // Example loading state from UI store
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartApi = useRef<IChartApi | null>(null);
  const candlestickSeries = useRef<ISeriesApi<'Candlestick'> | null>(null);

  // Memoize data to avoid re-renders if data reference is the same
  const memoizedData = useMemo(() => ohlcvData, [ohlcvData]);

  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create chart instance
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: chartContainerRef.current.clientHeight,
      // Add other chart options (layout, scales, etc.)
      layout: {
        background: { color: 'transparent' }, // Managed by parent container's background
        textColor: 'rgb(214, 217, 224)', // Example: theme-aware color
      },
      grid: {
        vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
        horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
      },
    });

    chartApi.current = chart;

    // Add candlestick series
    const series = chart.addCandlestickSeries({
      upColor: 'rgb(39, 174, 96)',
      downColor: 'rgb(231, 76, 60)',
      borderVisible: false,
      wickColor: 'rgb(200, 200, 200)',
    });
    candlestickSeries.current = series;

    // Handle window resize
    const handleResize = () => {
      if (chartContainerRef.current && chartApi.current) {
        chartApi.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
          height: chartContainerRef.current.clientHeight,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartApi.current) {
        chartApi.current.remove();
        chartApi.current = null;
      }
    };
  }, [/* No dependencies to run only once on mount */]);

  // Update data when ohlcvData changes
  useEffect(() => {
    if (candlestickSeries.current && memoizedData && memoizedData.length > 0) {
        // LW Charts expects time as a timestamp (seconds)
        const formattedData = memoizedData.map(d => ({
            time: d.time / 1000, // Convert ms to seconds
            open: d.open,
            high: d.high,
            low: d.low,
            close: d.close,
        }));
        candlestickSeries.current.setData(formattedData);
        // Optionally, auto-scale or fit content
        chartApi.current?.timeScale().fitContent();
    } else if (candlestickSeries.current) {
         // Clear data if no OHLCV data
         candlestickSeries.current.setData([]);
    }
  }, [memoizedData]); // Dependency on memoized data

  // Handle symbol or timeframe change (fetch historical data if needed)
  useEffect(() => {
      if (currentSymbol && timeframe) {
          // TODO: Fetch historical data for new symbol/timeframe
          // This is where REST API integration happens for historical data
          console.log(`Fetching historical data for ${currentSymbol}:${timeframe}`);
          // Example: setLoading(true); fetchDataService(currentSymbol, timeframe).then(data => useMarketDataStore.getState().setOHLCV(data)).finally(() => setLoading(false));
      }
  }, [currentSymbol, timeframe]); // Dependencies on symbol/timeframe state

  // TODO: Add logic for real-time data updates (append/update last bar)
  // This would typically come from a separate WS message type

  // TODO: Add logic for interactive order placement (event listeners on chart)

  // TODO: Add logic for adding/removing indicators (using LW Charts API)

  return (
    <div className="relative w-full h-full">
      {isLoading && <div className="absolute inset-0 flex items-center justify-center bg-background/80 z-10"><div className="spinner"></div></div>} {/* Simple loading overlay */}
      <div ref={chartContainerRef} className="w-full h-full"></div>
      {/* Add controls for indicators, timeframe, etc. */}
    </div>
  );
};

// Note: The actual data fetching and real-time update logic should be handled
// by the WebSocketManager and API services, updating the Zustand store.
// The component primarily reacts to changes in the store.
```

**Example 2: Component Consuming Zustand State with Memoization and Virtualization Hint**

```tsx
// RecentTradesPanel.tsx - Simplified Example
import React, { useMemo } from 'react';
import { useMarketDataStore } from '../state/useMarketDataStore';
// Assuming react-window is used for virtualization
import { FixedSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer'; // Helper to get parent dimensions

interface TradeRowProps {
  index: number;
  style: React.CSSProperties;
  data: { trades: typeof useMarketDataStore.getState().recentTrades };
}

// Memoize individual trade rows if they become complex
const TradeRow: React.FC<TradeRowProps> = React.memo(({ index, style, data }) => {
  const trade = data.trades[index];
  if (!trade) return null; // Handle potential data inconsistencies

  // Determine text color based on trade direction
  const textColor = trade.isBuyerMaker ? 'text-red-500' : 'text-green-500';

  return (
    <div style={style} className={`flex justify-between text-sm py-1 px-2 ${index % 2 === 0 ? 'bg-panel-bg-alt dark:bg-panel-bg-alt-dark' : ''}`}>
      <span className="w-1/3 text-left">{new Date(trade.time).toLocaleTimeString()}</span>
      <span className={`w-1/3 text-right ${textColor}`}>{trade.price.toFixed(2)}</span>{/* Format based on symbol precision */}
      <span className="w-1/3 text-right">{trade.size.toFixed(4)}</span>{/* Format based on symbol precision */}
    </div>
  );
});

export const RecentTradesPanel: React.FC = () => {
  const recentTrades = useMarketDataStore(state => state.recentTrades);

  // Memoize the trades data passed to the virtualized list
  const memoizedTrades = useMemo(() => recentTrades, [recentTrades]);

  return (
    <div className="flex flex-col h-full bg-panel-bg dark:bg-panel-bg-dark rounded shadow">
      <h3 className="text-lg font-semibold p-2 border-b border-border dark:border-border-dark">Recent Trades</h3>
      <div className="flex justify-between text-xs text-text-secondary dark:text-text-secondary-dark px-2 pb-1 border-b border-border dark:border-border-dark">
        <span className="w-1/3 text-left">Time</span>
        <span className="w-1/3 text-right">Price</span>
        <span className="w-1/3 text-right">Size</span>
      </div>
      <div className="flex-grow overflow-hidden"> {/* Container for AutoSizer */}
        <AutoSizer>
          {({ height, width }) => (
            <List
              height={height}
              itemCount={memoizedTrades.length}
              itemSize={24} // Approx height of each row
              width={width}
              itemData={{ trades: memoizedTrades }} // Pass data via itemData for memoized row component
            >
              {TradeRow}
            </List>
          )}
        </AutoSizer>
      </div>
    </div>
  );
};
```

---

This guide provides a foundational structure and key considerations for building the frontend of the Professional Web Trading Terminal. Adherence to the principles of modularity, performance optimization, and robust state/API management using the specified technologies will be crucial for the project's success.
```
