# Trading Competition Platform

A high-performance, real-time web trading terminal designed for competitive trading with live market data, user management, and automated prize distribution.

## 🚀 Features

- **Real-time Trading Terminal**: Professional-grade trading interface with TradingView charts
- **Competition Management**: Create and manage trading competitions with automated enrollment
- **Live Market Data**: Real-time price feeds and order book data via WebSocket
- **User Management**: Secure authentication and user profiles
- **Trade Tracking**: Comprehensive trade history and performance analytics
- **Leaderboards**: Real-time competition rankings and statistics
- **Prize Distribution**: Automated winner determination and prize allocation

## 🏗️ Architecture

### Frontend (React + TypeScript)
- **Framework**: React 18 with TypeScript
- **State Management**: Zustand for global state
- **Styling**: Tailwind CSS for responsive design
- **Charts**: TradingView Lightweight Charts
- **Real-time**: WebSocket connections for live data

### Backend (Node.js + NestJS)
- **Framework**: NestJS with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with refresh tokens
- **Real-time**: WebSocket Gateway for live updates
- **Caching**: Redis for session management and market data

### External Integrations
- **Market Data**: Binance API for real-time price feeds
- **Payments**: Stripe for entry fees and prize distribution
- **Notifications**: Email and push notifications

## 📦 Installation

### Prerequisites
- Node.js 18+ 
- PostgreSQL 14+
- Redis 6+
- npm or yarn

### Quick Start

1. **Clone and install dependencies**:
```bash
git clone <repository-url>
cd trading-competition-platform
npm run install:all
```

2. **Set up environment variables**:
```bash
# Copy environment templates
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

3. **Configure database**:
```bash
cd backend
npx prisma migrate dev
npx prisma generate
```

4. **Start development servers**:
```bash
npm run dev
```

- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- WebSocket: ws://localhost:3001/ws

## 🎯 Competition Flow

1. **Admin Creates Competition**: Set parameters, entry fee, prize pool, duration
2. **Users Enroll**: Register and pay entry fee to join competition
3. **Terminal Assignment**: Each user gets isolated trading environment
4. **Live Trading**: Real-time market data, order placement, position tracking
5. **Performance Tracking**: Continuous P&L calculation and ranking updates
6. **Winner Determination**: Automated based on performance metrics
7. **Prize Distribution**: Automatic payout to winners

## 🔧 Development

### Project Structure
```
├── frontend/          # React application
├── backend/           # NestJS API server
├── shared/            # Shared types and utilities
├── docs/              # Documentation
└── scripts/           # Build and deployment scripts
```

### Key Technologies
- **Frontend**: React, TypeScript, Zustand, Tailwind CSS, TradingView Charts
- **Backend**: NestJS, Prisma, PostgreSQL, Redis, WebSocket
- **DevOps**: Docker, GitHub Actions, AWS/Vercel deployment

## 📊 Performance Targets

- **Latency**: <100ms for order execution
- **Concurrent Users**: 10,000+ simultaneous connections
- **Data Updates**: <50ms for market data refresh
- **Uptime**: 99.9% availability during competitions

## 🔐 Security

- JWT authentication with refresh tokens
- Rate limiting on API endpoints
- Input validation and sanitization
- Encrypted WebSocket connections
- Secure payment processing

## 📈 Monitoring

- Real-time system metrics
- User activity tracking
- Competition analytics
- Performance monitoring
- Error logging and alerting

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [docs/](docs/)
