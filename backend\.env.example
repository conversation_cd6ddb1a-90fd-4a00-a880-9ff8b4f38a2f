# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/trading_competition?schema=public"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="1h"
JWT_REFRESH_SECRET="your-super-secret-refresh-key-change-this-in-production"
JWT_REFRESH_EXPIRES_IN="7d"

# Redis Configuration
REDIS_HOST="localhost"
REDIS_PORT=6379
REDIS_PASSWORD=""

# Server Configuration
PORT=3001
NODE_ENV="development"

# Binance API Configuration (for market data)
BINANCE_API_KEY="your-binance-api-key"
BINANCE_API_SECRET="your-binance-api-secret"
BINANCE_TESTNET=true

# Stripe Configuration (for payments)
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_secret"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# WebSocket Configuration
WS_PORT=3001
WS_PATH="/ws"

# Competition Configuration
DEFAULT_COMPETITION_DURATION=3600000  # 1 hour in milliseconds
MIN_ENTRY_FEE=10  # Minimum entry fee in USD
MAX_PARTICIPANTS=1000  # Maximum participants per competition

# Rate Limiting
RATE_LIMIT_TTL=60  # Time window in seconds
RATE_LIMIT_LIMIT=100  # Max requests per window

# Logging
LOG_LEVEL="debug"

# CORS Configuration
CORS_ORIGIN="http://localhost:3000"
