// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String?
  lastName  String?
  avatar    String?
  isActive  Boolean  @default(true)
  isA<PERSON><PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  competitions     CompetitionParticipant[]
  trades           Trade[]
  positions        Position[]
  notifications    Notification[]
  paymentMethods   PaymentMethod[]
  transactions     Transaction[]

  @@map("users")
}

model Competition {
  id          String            @id @default(uuid())
  name        String
  description String?
  entryFee    Decimal           @db.Decimal(10, 2)
  prizePool   Decimal           @db.Decimal(10, 2)
  maxParticipants Int
  startTime   DateTime
  endTime     DateTime
  status      CompetitionStatus @default(UPCOMING)
  rules       Json?             // Competition-specific rules
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  participants CompetitionParticipant[]
  trades       Trade[]
  leaderboard  Leaderboard[]

  @@map("competitions")
}

model CompetitionParticipant {
  id            String    @id @default(uuid())
  userId        String
  competitionId String
  joinedAt      DateTime  @default(now())
  initialBalance Decimal  @db.Decimal(15, 2) @default(10000.00)
  currentBalance Decimal  @db.Decimal(15, 2) @default(10000.00)
  totalPnl      Decimal   @db.Decimal(15, 2) @default(0.00)
  rank          Int?
  isActive      Boolean   @default(true)

  // Relations
  user        User        @relation(fields: [userId], references: [id])
  competition Competition @relation(fields: [competitionId], references: [id])

  @@unique([userId, competitionId])
  @@map("competition_participants")
}

model Trade {
  id            String      @id @default(uuid())
  userId        String
  competitionId String?
  symbol        String      // e.g., "BTCUSDT"
  side          TradeSide   // BUY or SELL
  type          TradeType   // MARKET, LIMIT, STOP, etc.
  quantity      Decimal     @db.Decimal(20, 8)
  price         Decimal?    @db.Decimal(20, 8)
  executedPrice Decimal?    @db.Decimal(20, 8)
  executedQty   Decimal?    @db.Decimal(20, 8)
  status        TradeStatus @default(PENDING)
  fee           Decimal?    @db.Decimal(10, 8)
  pnl           Decimal?    @db.Decimal(15, 2)
  executedAt    DateTime?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  user        User         @relation(fields: [userId], references: [id])
  competition Competition? @relation(fields: [competitionId], references: [id])

  @@map("trades")
}

model Position {
  id            String   @id @default(uuid())
  userId        String
  symbol        String
  side          TradeSide
  quantity      Decimal  @db.Decimal(20, 8)
  avgPrice      Decimal  @db.Decimal(20, 8)
  currentPrice  Decimal? @db.Decimal(20, 8)
  unrealizedPnl Decimal? @db.Decimal(15, 2)
  realizedPnl   Decimal  @db.Decimal(15, 2) @default(0.00)
  isOpen        Boolean  @default(true)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@unique([userId, symbol, side])
  @@map("positions")
}

model Leaderboard {
  id            String   @id @default(uuid())
  competitionId String
  userId        String
  rank          Int
  totalPnl      Decimal  @db.Decimal(15, 2)
  totalTrades   Int      @default(0)
  winRate       Decimal? @db.Decimal(5, 2)
  updatedAt     DateTime @updatedAt

  // Relations
  competition Competition @relation(fields: [competitionId], references: [id])

  @@unique([competitionId, userId])
  @@map("leaderboard")
}

model PaymentMethod {
  id        String            @id @default(uuid())
  userId    String
  type      PaymentMethodType
  provider  String            // stripe, paypal, etc.
  externalId String           // Stripe payment method ID
  isDefault Boolean           @default(false)
  createdAt DateTime          @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("payment_methods")
}

model Transaction {
  id            String            @id @default(uuid())
  userId        String
  type          TransactionType
  amount        Decimal           @db.Decimal(10, 2)
  currency      String            @default("USD")
  status        TransactionStatus @default(PENDING)
  externalId    String?           // Stripe payment intent ID
  description   String?
  metadata      Json?
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("transactions")
}

model Notification {
  id        String           @id @default(uuid())
  userId    String
  type      NotificationType
  title     String
  message   String
  isRead    Boolean          @default(false)
  metadata  Json?
  createdAt DateTime         @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("notifications")
}

// Enums
enum CompetitionStatus {
  UPCOMING
  ACTIVE
  COMPLETED
  CANCELLED
}

enum TradeSide {
  BUY
  SELL
}

enum TradeType {
  MARKET
  LIMIT
  STOP
  STOP_LIMIT
}

enum TradeStatus {
  PENDING
  FILLED
  PARTIALLY_FILLED
  CANCELLED
  REJECTED
}

enum PaymentMethodType {
  CREDIT_CARD
  DEBIT_CARD
  BANK_ACCOUNT
  PAYPAL
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  ENTRY_FEE
  PRIZE_PAYOUT
  REFUND
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum NotificationType {
  TRADE_EXECUTED
  COMPETITION_STARTED
  COMPETITION_ENDED
  PRIZE_WON
  SYSTEM_ALERT
}
