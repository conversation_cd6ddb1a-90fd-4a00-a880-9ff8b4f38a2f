version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: trading-postgres
    environment:
      POSTGRES_DB: trading_competition
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/prisma/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - trading-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: trading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - trading-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: trading-backend
    environment:
      DATABASE_URL: ********************************************/trading_competition?schema=public
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: your-super-secret-jwt-key-change-this-in-production
      JWT_REFRESH_SECRET: your-super-secret-refresh-key-change-this-in-production
      NODE_ENV: development
      PORT: 3001
    ports:
      - "3001:3001"
    depends_on:
      - postgres
      - redis
    volumes:
      - ./backend:/app
      - /app/node_modules
    networks:
      - trading-network
    command: npm run dev

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: trading-frontend
    environment:
      VITE_API_BASE_URL: http://localhost:3001/api/v1
      VITE_WS_URL: ws://localhost:3001/ws
    ports:
      - "3000:3000"
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - trading-network
    command: npm run dev

volumes:
  postgres_data:
  redis_data:

networks:
  trading-network:
    driver: bridge
