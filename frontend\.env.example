# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api/v1
VITE_WS_URL=ws://localhost:3001/ws

# Environment
VITE_NODE_ENV=development

# Stripe Configuration (for payments)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

# Feature Flags
VITE_ENABLE_DEMO_MODE=true
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_SOUND_ALERTS=true

# Chart Configuration
VITE_DEFAULT_SYMBOL=BTCUSDT
VITE_DEFAULT_TIMEFRAME=1m
VITE_CHART_THEME=dark

# Competition Configuration
VITE_DEFAULT_BALANCE=10000
VITE_MIN_TRADE_AMOUNT=1

# WebSocket Configuration
VITE_WS_RECONNECT_INTERVAL=5000
VITE_WS_MAX_RECONNECT_ATTEMPTS=10

# UI Configuration
VITE_DEFAULT_THEME=dark
VITE_ENABLE_ANIMATIONS=true
