<!doctype html>
<html lang="en" class="dark">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Trading Terminal - Professional Trading Platform</title>
    <meta name="description" content="Professional trading terminal with real-time market data, advanced charting, and order management" />
    
    <!-- Preload critical fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <style>
      /* Prevent flash of unstyled content */
      body {
        background-color: #0f172a;
        color: #f1f5f9;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
      }
      
      /* Loading spinner */
      .loading-spinner {
        display: inline-block;
        width: 20px;
        height: 20px;
        border: 3px solid #334155;
        border-radius: 50%;
        border-top-color: #3b82f6;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      /* Hide scrollbar but keep functionality */
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
      
      ::-webkit-scrollbar-track {
        background: #1e293b;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #475569;
        border-radius: 3px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #64748b;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- Loading fallback -->
      <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column;">
        <div class="loading-spinner"></div>
        <p style="margin-top: 16px; color: #64748b; font-size: 14px;">Loading Trading Terminal...</p>
      </div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
