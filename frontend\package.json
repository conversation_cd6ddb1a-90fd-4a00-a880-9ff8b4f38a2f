{"name": "trading-competition-frontend", "version": "1.0.0", "private": true, "description": "Frontend for trading competition platform", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.1", "zustand": "^4.3.9", "lightweight-charts": "^4.1.1", "axios": "^1.4.0", "socket.io-client": "^4.7.1", "react-hook-form": "^7.45.1", "react-query": "^3.39.3", "@hookform/resolvers": "^3.1.1", "zod": "^3.21.4", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1", "lucide-react": "^0.263.1", "clsx": "^1.2.1", "tailwind-merge": "^1.13.2", "framer-motion": "^10.12.18", "react-virtualized": "^9.22.5", "recharts": "^2.7.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-virtualized": "^9.21.21", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.1", "@vitest/ui": "^0.34.1", "jsdom": "^22.1.0", "@tailwindcss/forms": "^0.5.4", "@tailwindcss/typography": "^0.5.9"}}