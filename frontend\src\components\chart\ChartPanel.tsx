import React, { useEffect, useRef, useState } from 'react';
import { create<PERSON>hart, IChartApi, ISeriesApi, CandlestickData, Time } from 'lightweight-charts';
import { BarChart3, TrendingUp, Settings } from 'lucide-react';

interface ChartPanelProps {
  symbol: string;
}

const ChartPanel: React.FC<ChartPanelProps> = ({ symbol }) => {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<IChartApi | null>(null);
  const candlestickSeriesRef = useRef<ISeriesApi<'Candlestick'> | null>(null);
  const [timeframe, setTimeframe] = useState('1m');
  const [chartType, setChartType] = useState<'candlestick' | 'line'>('candlestick');

  const timeframes = [
    { label: '1m', value: '1m' },
    { label: '5m', value: '5m' },
    { label: '15m', value: '15m' },
    { label: '1h', value: '1h' },
    { label: '4h', value: '4h' },
    { label: '1d', value: '1d' },
  ];

  useEffect(() => {
    if (!chartContainerRef.current) return;

    // Create chart
    const chart = createChart(chartContainerRef.current, {
      width: chartContainerRef.current.clientWidth,
      height: chartContainerRef.current.clientHeight,
      layout: {
        background: { color: '#0f172a' },
        textColor: '#cbd5e1',
      },
      grid: {
        vertLines: { color: '#1e293b' },
        horzLines: { color: '#1e293b' },
      },
      crosshair: {
        mode: 1,
        vertLine: {
          color: '#475569',
          width: 1,
          style: 2,
        },
        horzLine: {
          color: '#475569',
          width: 1,
          style: 2,
        },
      },
      rightPriceScale: {
        borderColor: '#334155',
        textColor: '#cbd5e1',
      },
      timeScale: {
        borderColor: '#334155',
        textColor: '#cbd5e1',
        timeVisible: true,
        secondsVisible: false,
      },
    });

    chartRef.current = chart;

    // Create candlestick series
    const candlestickSeries = chart.addCandlestickSeries({
      upColor: '#22c55e',
      downColor: '#ef4444',
      borderDownColor: '#ef4444',
      borderUpColor: '#22c55e',
      wickDownColor: '#ef4444',
      wickUpColor: '#22c55e',
    });

    candlestickSeriesRef.current = candlestickSeries;

    // Generate sample data (in real app, this would come from API)
    const generateSampleData = (): CandlestickData[] => {
      const data: CandlestickData[] = [];
      const basePrice = 45000;
      let currentPrice = basePrice;
      const now = Date.now();

      for (let i = 0; i < 100; i++) {
        const time = (now - (100 - i) * 60 * 1000) / 1000 as Time;
        const change = (Math.random() - 0.5) * 200;
        const open = currentPrice;
        const close = currentPrice + change;
        const high = Math.max(open, close) + Math.random() * 50;
        const low = Math.min(open, close) - Math.random() * 50;

        data.push({
          time,
          open,
          high,
          low,
          close,
        });

        currentPrice = close;
      }

      return data;
    };

    candlestickSeries.setData(generateSampleData());

    // Handle resize
    const handleResize = () => {
      if (chartContainerRef.current && chartRef.current) {
        chartRef.current.applyOptions({
          width: chartContainerRef.current.clientWidth,
          height: chartContainerRef.current.clientHeight,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      if (chartRef.current) {
        chartRef.current.remove();
      }
    };
  }, [symbol]);

  const handleTimeframeChange = (newTimeframe: string) => {
    setTimeframe(newTimeframe);
    // In real app, fetch new data for the timeframe
  };

  const toggleChartType = () => {
    setChartType(prev => prev === 'candlestick' ? 'line' : 'candlestick');
    // In real app, switch between chart types
  };

  return (
    <div className="trading-panel h-full">
      {/* Chart Header */}
      <div className="trading-panel-header">
        <div className="flex items-center space-x-4">
          <h3 className="text-sm font-medium">{symbol} Chart</h3>
          
          {/* Timeframe Selector */}
          <div className="flex items-center space-x-1">
            {timeframes.map((tf) => (
              <button
                key={tf.value}
                onClick={() => handleTimeframeChange(tf.value)}
                className={`px-2 py-1 text-xs rounded transition-colors ${
                  timeframe === tf.value
                    ? 'bg-blue-600 text-white'
                    : 'text-gray-400 hover:text-gray-200 hover:bg-dark-700'
                }`}
              >
                {tf.label}
              </button>
            ))}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Chart Type Toggle */}
          <button
            onClick={toggleChartType}
            className="p-1 hover:bg-dark-700 rounded transition-colors"
            title={`Switch to ${chartType === 'candlestick' ? 'line' : 'candlestick'} chart`}
          >
            {chartType === 'candlestick' ? (
              <TrendingUp className="w-4 h-4 text-gray-400" />
            ) : (
              <BarChart3 className="w-4 h-4 text-gray-400" />
            )}
          </button>

          {/* Chart Settings */}
          <button
            className="p-1 hover:bg-dark-700 rounded transition-colors"
            title="Chart settings"
          >
            <Settings className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative flex-1">
        <div
          ref={chartContainerRef}
          className="absolute inset-0 chart-container"
        />
        
        {/* Chart Loading Overlay */}
        {!chartRef.current && (
          <div className="absolute inset-0 flex items-center justify-center bg-chart-background">
            <div className="text-center">
              <div className="spinner mx-auto mb-2" />
              <p className="text-sm text-gray-400">Loading chart...</p>
            </div>
          </div>
        )}

        {/* Chart Tools Overlay */}
        <div className="absolute top-4 left-4 flex flex-col space-y-2">
          <button className="p-2 bg-dark-800/80 hover:bg-dark-700 rounded border border-dark-600 transition-colors">
            <span className="text-xs text-gray-300">📏</span>
          </button>
          <button className="p-2 bg-dark-800/80 hover:bg-dark-700 rounded border border-dark-600 transition-colors">
            <span className="text-xs text-gray-300">📐</span>
          </button>
          <button className="p-2 bg-dark-800/80 hover:bg-dark-700 rounded border border-dark-600 transition-colors">
            <span className="text-xs text-gray-300">✏️</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ChartPanel;
