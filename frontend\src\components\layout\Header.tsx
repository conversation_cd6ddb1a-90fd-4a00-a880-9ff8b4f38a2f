import React, { useState } from 'react';
import { <PERSON>, <PERSON>tings, Moon, Sun, Wifi, WifiOff } from 'lucide-react';
import { useTheme } from '../../contexts/ThemeContext';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { useMarketData } from '../../hooks/useMarketData';

const Header: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { isConnected } = useWebSocket();
  const { currentSymbol, ticker, changeSymbol } = useMarketData();
  const [searchOpen, setSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const popularSymbols = [
    'BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT',
    'XRPUSDT', 'DOTUSDT', 'DOGEUSDT', 'AVAXUSDT', 'MATICUSDT'
  ];

  const filteredSymbols = popularSymbols.filter(symbol =>
    symbol.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatPrice = (price: string) => {
    return parseFloat(price).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
    });
  };

  const formatPercentage = (percent: string) => {
    const value = parseFloat(percent);
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  return (
    <div className="trading-panel h-full">
      <div className="flex items-center justify-between h-full px-4">
        {/* Left Section - Logo & Symbol */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">T</span>
            </div>
            <span className="text-lg font-bold text-gray-200">Terminal</span>
          </div>

          {/* Symbol Selector */}
          <div className="relative">
            <button
              onClick={() => setSearchOpen(!searchOpen)}
              className="flex items-center space-x-2 bg-dark-700 hover:bg-dark-600 px-3 py-2 rounded-md transition-colors"
            >
              <span className="font-bold text-lg">{currentSymbol}</span>
              <Search className="w-4 h-4 text-gray-400" />
            </button>

            {searchOpen && (
              <div className="absolute top-full left-0 mt-2 w-64 bg-dark-800 border border-dark-600 rounded-lg shadow-xl z-50">
                <div className="p-3 border-b border-dark-600">
                  <input
                    type="text"
                    placeholder="Search symbols..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full input-primary text-sm"
                    autoFocus
                  />
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {filteredSymbols.map((symbol) => (
                    <button
                      key={symbol}
                      onClick={() => {
                        changeSymbol(symbol);
                        setSearchOpen(false);
                        setSearchQuery('');
                      }}
                      className={`w-full text-left px-3 py-2 hover:bg-dark-700 transition-colors ${
                        symbol === currentSymbol ? 'bg-dark-700 text-blue-400' : 'text-gray-300'
                      }`}
                    >
                      {symbol}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Center Section - Price Info */}
        <div className="flex items-center space-x-6">
          {ticker && (
            <>
              <div className="text-center">
                <div className="text-xs text-gray-400">Price</div>
                <div className={`text-lg font-bold font-mono ${
                  parseFloat(ticker.priceChange) >= 0 ? 'text-bull-500' : 'text-bear-500'
                }`}>
                  ${formatPrice(ticker.price)}
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-xs text-gray-400">24h Change</div>
                <div className={`text-sm font-mono ${
                  parseFloat(ticker.priceChange) >= 0 ? 'text-bull-500' : 'text-bear-500'
                }`}>
                  {formatPercentage(ticker.priceChangePercent)}
                </div>
              </div>

              <div className="text-center">
                <div className="text-xs text-gray-400">24h High</div>
                <div className="text-sm font-mono text-gray-300">
                  ${formatPrice(ticker.high)}
                </div>
              </div>

              <div className="text-center">
                <div className="text-xs text-gray-400">24h Low</div>
                <div className="text-sm font-mono text-gray-300">
                  ${formatPrice(ticker.low)}
                </div>
              </div>

              <div className="text-center">
                <div className="text-xs text-gray-400">24h Volume</div>
                <div className="text-sm font-mono text-gray-300">
                  {parseFloat(ticker.volume).toLocaleString()} {currentSymbol.replace('USDT', '')}
                </div>
              </div>
            </>
          )}
        </div>

        {/* Right Section - Controls */}
        <div className="flex items-center space-x-3">
          {/* Connection Status */}
          <div className="flex items-center space-x-2">
            {isConnected ? (
              <Wifi className="w-4 h-4 text-bull-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-bear-500" />
            )}
            <span className={`text-xs ${isConnected ? 'text-bull-500' : 'text-bear-500'}`}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </span>
          </div>

          {/* Theme Toggle */}
          <button
            onClick={toggleTheme}
            className="p-2 hover:bg-dark-700 rounded-md transition-colors"
            title={`Switch to ${theme === 'dark' ? 'light' : 'dark'} theme`}
          >
            {theme === 'dark' ? (
              <Sun className="w-4 h-4 text-gray-400" />
            ) : (
              <Moon className="w-4 h-4 text-gray-400" />
            )}
          </button>

          {/* Settings */}
          <button
            className="p-2 hover:bg-dark-700 rounded-md transition-colors"
            title="Settings"
          >
            <Settings className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>

      {/* Close search overlay when clicking outside */}
      {searchOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setSearchOpen(false)}
        />
      )}
    </div>
  );
};

export default Header;
