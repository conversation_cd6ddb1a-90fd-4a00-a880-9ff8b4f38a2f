import React, { useMemo } from 'react';
import { useMarketData } from '../../hooks/useMarketData';

interface OrderBookPanelProps {
  symbol: string;
}

const OrderBookPanel: React.FC<OrderBookPanelProps> = ({ symbol }) => {
  const { orderBook, loading } = useMarketData(symbol);

  const { processedBids, processedAsks, spread, spreadPercent } = useMemo(() => {
    if (!orderBook) {
      return { processedBids: [], processedAsks: [], spread: 0, spreadPercent: 0 };
    }

    // Process bids (highest to lowest)
    const bids = orderBook.bids.slice(0, 15).map(([price, quantity]) => {
      const priceNum = parseFloat(price);
      const quantityNum = parseFloat(quantity);
      return {
        price: priceNum,
        quantity: quantityNum,
        total: priceNum * quantityNum,
        priceStr: priceNum.toFixed(2),
        quantityStr: quantityNum.toFixed(6),
      };
    });

    // Process asks (lowest to highest)
    const asks = orderBook.asks.slice(0, 15).map(([price, quantity]) => {
      const priceNum = parseFloat(price);
      const quantityNum = parseFloat(quantity);
      return {
        price: priceNum,
        quantity: quantityNum,
        total: priceNum * quantityNum,
        priceStr: priceNum.toFixed(2),
        quantityStr: quantityNum.toFixed(6),
      };
    });

    // Calculate spread
    const bestBid = bids[0]?.price || 0;
    const bestAsk = asks[0]?.price || 0;
    const spreadValue = bestAsk - bestBid;
    const spreadPercentValue = bestBid > 0 ? (spreadValue / bestBid) * 100 : 0;

    return {
      processedBids: bids,
      processedAsks: asks,
      spread: spreadValue,
      spreadPercent: spreadPercentValue,
    };
  }, [orderBook]);

  const maxBidQuantity = Math.max(...processedBids.map(b => b.quantity), 1);
  const maxAskQuantity = Math.max(...processedAsks.map(a => a.quantity), 1);

  if (loading) {
    return (
      <div className="trading-panel h-full">
        <div className="trading-panel-header">
          <h3 className="text-sm font-medium">Order Book</h3>
        </div>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="spinner mx-auto mb-2" />
            <p className="text-sm text-gray-400">Loading order book...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="trading-panel h-full flex flex-col">
      {/* Header */}
      <div className="trading-panel-header">
        <h3 className="text-sm font-medium">Order Book</h3>
        <div className="text-xs text-gray-400">
          Spread: {spread.toFixed(2)} ({spreadPercent.toFixed(3)}%)
        </div>
      </div>

      {/* Column Headers */}
      <div className="px-4 py-2 border-b border-dark-700">
        <div className="grid grid-cols-3 gap-2 text-xs text-gray-400 font-medium">
          <div className="text-left">Price (USDT)</div>
          <div className="text-right">Amount ({symbol.replace('USDT', '')})</div>
          <div className="text-right">Total</div>
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        {/* Asks (Sell Orders) - Red */}
        <div className="h-1/2 overflow-y-auto">
          <div className="space-y-0">
            {processedAsks.reverse().map((ask, index) => {
              const widthPercent = (ask.quantity / maxAskQuantity) * 100;
              return (
                <div
                  key={`ask-${ask.price}-${index}`}
                  className="relative orderbook-row orderbook-ask cursor-pointer"
                  style={{
                    background: `linear-gradient(to left, rgba(239, 68, 68, 0.1) ${widthPercent}%, transparent ${widthPercent}%)`,
                  }}
                >
                  <div className="text-bear-400 font-mono">{ask.priceStr}</div>
                  <div className="text-right text-gray-300 font-mono">{ask.quantityStr}</div>
                  <div className="text-right text-gray-400 font-mono text-xs">
                    {ask.total.toLocaleString()}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Spread Indicator */}
        <div className="py-2 px-4 bg-dark-900 border-y border-dark-600">
          <div className="text-center">
            <div className="text-xs text-gray-500">Spread</div>
            <div className="text-sm font-mono text-gray-300">
              {spread.toFixed(2)} ({spreadPercent.toFixed(3)}%)
            </div>
          </div>
        </div>

        {/* Bids (Buy Orders) - Green */}
        <div className="h-1/2 overflow-y-auto">
          <div className="space-y-0">
            {processedBids.map((bid, index) => {
              const widthPercent = (bid.quantity / maxBidQuantity) * 100;
              return (
                <div
                  key={`bid-${bid.price}-${index}`}
                  className="relative orderbook-row orderbook-bid cursor-pointer"
                  style={{
                    background: `linear-gradient(to left, rgba(34, 197, 94, 0.1) ${widthPercent}%, transparent ${widthPercent}%)`,
                  }}
                >
                  <div className="text-bull-400 font-mono">{bid.priceStr}</div>
                  <div className="text-right text-gray-300 font-mono">{bid.quantityStr}</div>
                  <div className="text-right text-gray-400 font-mono text-xs">
                    {bid.total.toLocaleString()}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Footer with aggregation controls */}
      <div className="px-4 py-2 border-t border-dark-700">
        <div className="flex items-center justify-between text-xs">
          <span className="text-gray-400">Precision:</span>
          <div className="flex space-x-1">
            {[0.01, 0.1, 1, 10].map((precision) => (
              <button
                key={precision}
                className="px-2 py-1 text-gray-400 hover:text-gray-200 hover:bg-dark-700 rounded transition-colors"
              >
                {precision}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderBookPanel;
