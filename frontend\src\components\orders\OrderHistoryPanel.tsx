import React, { useState } from 'react';
import { Filter, Download } from 'lucide-react';

interface Order {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop';
  quantity: number;
  price: number;
  filled: number;
  status: 'filled' | 'partial' | 'cancelled' | 'pending';
  time: number;
  fee: number;
}

const OrderHistoryPanel: React.FC = () => {
  const [filter, setFilter] = useState<'all' | 'filled' | 'cancelled'>('all');

  // Mock order history data
  const orders: Order[] = [
    {
      id: '1',
      symbol: 'BTCUSDT',
      side: 'buy',
      type: 'limit',
      quantity: 0.1,
      price: 44500,
      filled: 0.1,
      status: 'filled',
      time: Date.now() - 3600000,
      fee: 4.45,
    },
    {
      id: '2',
      symbol: 'ETHUSDT',
      side: 'sell',
      type: 'market',
      quantity: 2.5,
      price: 2800,
      filled: 2.5,
      status: 'filled',
      time: Date.now() - 7200000,
      fee: 7.0,
    },
    {
      id: '3',
      symbol: 'BTCUSDT',
      side: 'buy',
      type: 'limit',
      quantity: 0.05,
      price: 43000,
      filled: 0,
      status: 'cancelled',
      time: Date.now() - 10800000,
      fee: 0,
    },
  ];

  const filteredOrders = orders.filter(order => {
    if (filter === 'all') return true;
    return order.status === filter;
  });

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('en-US', {
      month: 'short',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatPrice = (price: number) => {
    return price.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'filled':
        return 'text-bull-500';
      case 'partial':
        return 'text-yellow-500';
      case 'cancelled':
        return 'text-gray-500';
      case 'pending':
        return 'text-blue-500';
      default:
        return 'text-gray-400';
    }
  };

  const getSideColor = (side: string) => {
    return side === 'buy' ? 'text-bull-500' : 'text-bear-500';
  };

  return (
    <div className="trading-panel h-full flex flex-col">
      {/* Header */}
      <div className="trading-panel-header">
        <h3 className="text-sm font-medium">Order History</h3>
        <div className="flex items-center space-x-2">
          {/* Filter Dropdown */}
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value as any)}
            className="text-xs bg-dark-700 border border-dark-600 text-gray-200 px-2 py-1 rounded"
          >
            <option value="all">All Orders</option>
            <option value="filled">Filled</option>
            <option value="cancelled">Cancelled</option>
          </select>

          {/* Export Button */}
          <button
            className="p-1 hover:bg-dark-700 rounded transition-colors"
            title="Export orders"
          >
            <Download className="w-4 h-4 text-gray-400" />
          </button>
        </div>
      </div>

      {/* Column Headers */}
      <div className="px-4 py-2 border-b border-dark-700">
        <div className="grid grid-cols-9 gap-2 text-xs text-gray-400 font-medium">
          <div>Time</div>
          <div>Symbol</div>
          <div>Side</div>
          <div>Type</div>
          <div>Quantity</div>
          <div>Price</div>
          <div>Filled</div>
          <div>Status</div>
          <div>Fee</div>
        </div>
      </div>

      {/* Orders List */}
      <div className="flex-1 overflow-y-auto">
        {filteredOrders.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-2">📋</div>
              <p className="text-sm text-gray-400">
                {filter === 'all' ? 'No order history' : `No ${filter} orders`}
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-0">
            {filteredOrders.map((order) => (
              <div
                key={order.id}
                className="grid grid-cols-9 gap-2 py-2 px-4 text-sm hover:bg-dark-700 transition-colors border-b border-dark-800"
              >
                {/* Time */}
                <div className="text-gray-400 text-xs">
                  {formatTime(order.time)}
                </div>

                {/* Symbol */}
                <div className="font-medium text-gray-200">
                  {order.symbol}
                </div>

                {/* Side */}
                <div className={`font-medium ${getSideColor(order.side)}`}>
                  {order.side.toUpperCase()}
                </div>

                {/* Type */}
                <div className="text-gray-300 capitalize">
                  {order.type}
                </div>

                {/* Quantity */}
                <div className="font-mono text-gray-300">
                  {order.quantity}
                </div>

                {/* Price */}
                <div className="font-mono text-gray-300">
                  ${formatPrice(order.price)}
                </div>

                {/* Filled */}
                <div className="font-mono text-gray-300">
                  {order.filled}
                  {order.filled < order.quantity && (
                    <span className="text-xs text-gray-500">
                      /{order.quantity}
                    </span>
                  )}
                </div>

                {/* Status */}
                <div className={`font-medium capitalize ${getStatusColor(order.status)}`}>
                  {order.status}
                </div>

                {/* Fee */}
                <div className="font-mono text-gray-400 text-xs">
                  ${order.fee.toFixed(2)}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Summary Footer */}
      {filteredOrders.length > 0 && (
        <div className="px-4 py-3 border-t border-dark-700 bg-dark-900">
          <div className="grid grid-cols-4 gap-4 text-xs">
            <div>
              <span className="text-gray-400">Total Orders:</span>
              <div className="font-mono text-gray-300">
                {filteredOrders.length}
              </div>
            </div>
            <div>
              <span className="text-gray-400">Filled:</span>
              <div className="font-mono text-bull-500">
                {filteredOrders.filter(o => o.status === 'filled').length}
              </div>
            </div>
            <div>
              <span className="text-gray-400">Cancelled:</span>
              <div className="font-mono text-gray-500">
                {filteredOrders.filter(o => o.status === 'cancelled').length}
              </div>
            </div>
            <div>
              <span className="text-gray-400">Total Fees:</span>
              <div className="font-mono text-gray-300">
                ${filteredOrders.reduce((sum, o) => sum + o.fee, 0).toFixed(2)}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderHistoryPanel;
