import React, { useState } from 'react';
import { Calculator, TrendingUp, TrendingDown } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface OrderPanelProps {
  symbol: string;
}

type OrderType = 'market' | 'limit' | 'stop' | 'stop-limit';
type OrderSide = 'buy' | 'sell';

const OrderPanel: React.FC<OrderPanelProps> = ({ symbol }) => {
  const [orderType, setOrderType] = useState<OrderType>('limit');
  const [orderSide, setOrderSide] = useState<OrderSide>('buy');
  const [price, setPrice] = useState('');
  const [quantity, setQuantity] = useState('');
  const [stopPrice, setStopPrice] = useState('');
  const [total, setTotal] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Mock balance data
  const balance = {
    USDT: 10000,
    BTC: 0.5,
  };

  const baseAsset = symbol.replace('USDT', '');
  const quoteAsset = 'USDT';

  const orderTypes = [
    { value: 'market', label: 'Market' },
    { value: 'limit', label: 'Limit' },
    { value: 'stop', label: 'Stop' },
    { value: 'stop-limit', label: 'Stop Limit' },
  ];

  const calculateTotal = () => {
    if (price && quantity) {
      const totalValue = parseFloat(price) * parseFloat(quantity);
      setTotal(totalValue.toFixed(2));
    }
  };

  const handlePriceChange = (value: string) => {
    setPrice(value);
    if (value && quantity) {
      const totalValue = parseFloat(value) * parseFloat(quantity);
      setTotal(totalValue.toFixed(2));
    }
  };

  const handleQuantityChange = (value: string) => {
    setQuantity(value);
    if (price && value) {
      const totalValue = parseFloat(price) * parseFloat(value);
      setTotal(totalValue.toFixed(2));
    }
  };

  const handleTotalChange = (value: string) => {
    setTotal(value);
    if (price && value) {
      const quantityValue = parseFloat(value) / parseFloat(price);
      setQuantity(quantityValue.toFixed(6));
    }
  };

  const handlePercentageClick = (percentage: number) => {
    const availableBalance = orderSide === 'buy' ? balance.USDT : balance[baseAsset as keyof typeof balance] || 0;
    
    if (orderSide === 'buy' && price) {
      const maxQuantity = (availableBalance * percentage / 100) / parseFloat(price);
      setQuantity(maxQuantity.toFixed(6));
      setTotal((availableBalance * percentage / 100).toFixed(2));
    } else if (orderSide === 'sell') {
      const sellQuantity = availableBalance * percentage / 100;
      setQuantity(sellQuantity.toFixed(6));
      if (price) {
        setTotal((sellQuantity * parseFloat(price)).toFixed(2));
      }
    }
  };

  const handleSubmitOrder = async () => {
    if (!quantity || (orderType !== 'market' && !price)) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast.success(`${orderSide.toUpperCase()} order placed successfully!`);
      
      // Reset form
      setPrice('');
      setQuantity('');
      setTotal('');
      setStopPrice('');
    } catch (error) {
      toast.error('Failed to place order');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="trading-panel h-full flex flex-col">
      {/* Header */}
      <div className="trading-panel-header">
        <h3 className="text-sm font-medium">Place Order</h3>
        <div className="text-xs text-gray-400">
          {symbol}
        </div>
      </div>

      <div className="flex-1 p-4 space-y-4">
        {/* Order Side Tabs */}
        <div className="grid grid-cols-2 gap-1 p-1 bg-dark-900 rounded-lg">
          <button
            onClick={() => setOrderSide('buy')}
            className={`py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              orderSide === 'buy'
                ? 'bg-bull-600 text-white'
                : 'text-gray-400 hover:text-gray-200'
            }`}
          >
            <TrendingUp className="w-4 h-4 inline mr-1" />
            Buy
          </button>
          <button
            onClick={() => setOrderSide('sell')}
            className={`py-2 px-4 rounded-md text-sm font-medium transition-colors ${
              orderSide === 'sell'
                ? 'bg-bear-600 text-white'
                : 'text-gray-400 hover:text-gray-200'
            }`}
          >
            <TrendingDown className="w-4 h-4 inline mr-1" />
            Sell
          </button>
        </div>

        {/* Order Type Selector */}
        <div>
          <label className="block text-xs text-gray-400 mb-2">Order Type</label>
          <select
            value={orderType}
            onChange={(e) => setOrderType(e.target.value as OrderType)}
            className="w-full input-primary text-sm"
          >
            {orderTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </div>

        {/* Stop Price (for stop orders) */}
        {(orderType === 'stop' || orderType === 'stop-limit') && (
          <div>
            <label className="block text-xs text-gray-400 mb-2">Stop Price</label>
            <input
              type="number"
              value={stopPrice}
              onChange={(e) => setStopPrice(e.target.value)}
              placeholder="0.00"
              className="w-full input-primary text-sm font-mono"
            />
          </div>
        )}

        {/* Price (not for market orders) */}
        {orderType !== 'market' && (
          <div>
            <label className="block text-xs text-gray-400 mb-2">
              Price ({quoteAsset})
            </label>
            <input
              type="number"
              value={price}
              onChange={(e) => handlePriceChange(e.target.value)}
              placeholder="0.00"
              className="w-full input-primary text-sm font-mono"
            />
          </div>
        )}

        {/* Quantity */}
        <div>
          <label className="block text-xs text-gray-400 mb-2">
            Amount ({baseAsset})
          </label>
          <input
            type="number"
            value={quantity}
            onChange={(e) => handleQuantityChange(e.target.value)}
            placeholder="0.000000"
            className="w-full input-primary text-sm font-mono"
          />
        </div>

        {/* Percentage Buttons */}
        <div className="grid grid-cols-4 gap-2">
          {[25, 50, 75, 100].map((percentage) => (
            <button
              key={percentage}
              onClick={() => handlePercentageClick(percentage)}
              className="py-1 px-2 text-xs bg-dark-700 hover:bg-dark-600 text-gray-300 rounded transition-colors"
            >
              {percentage}%
            </button>
          ))}
        </div>

        {/* Total */}
        {orderType !== 'market' && (
          <div>
            <label className="block text-xs text-gray-400 mb-2">
              Total ({quoteAsset})
            </label>
            <input
              type="number"
              value={total}
              onChange={(e) => handleTotalChange(e.target.value)}
              placeholder="0.00"
              className="w-full input-primary text-sm font-mono"
            />
          </div>
        )}

        {/* Balance Info */}
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span className="text-gray-400">Available:</span>
            <span className="font-mono">
              {orderSide === 'buy' 
                ? `${balance.USDT.toLocaleString()} ${quoteAsset}`
                : `${(balance[baseAsset as keyof typeof balance] || 0).toFixed(6)} ${baseAsset}`
              }
            </span>
          </div>
        </div>

        {/* Submit Button */}
        <button
          onClick={handleSubmitOrder}
          disabled={isLoading || !quantity}
          className={`w-full py-3 rounded-md font-medium transition-colors ${
            orderSide === 'buy'
              ? 'btn-success'
              : 'btn-danger'
          } disabled:opacity-50 disabled:cursor-not-allowed`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center">
              <div className="spinner mr-2" />
              Placing Order...
            </div>
          ) : (
            `${orderSide === 'buy' ? 'Buy' : 'Sell'} ${baseAsset}`
          )}
        </button>

        {/* Order Summary */}
        {quantity && (orderType === 'market' || price) && (
          <div className="p-3 bg-dark-900 rounded-lg border border-dark-600">
            <div className="text-xs text-gray-400 mb-2">Order Summary</div>
            <div className="space-y-1 text-xs">
              <div className="flex justify-between">
                <span>Side:</span>
                <span className={orderSide === 'buy' ? 'text-bull-400' : 'text-bear-400'}>
                  {orderSide.toUpperCase()}
                </span>
              </div>
              <div className="flex justify-between">
                <span>Type:</span>
                <span>{orderType.toUpperCase()}</span>
              </div>
              <div className="flex justify-between">
                <span>Amount:</span>
                <span className="font-mono">{quantity} {baseAsset}</span>
              </div>
              {orderType !== 'market' && (
                <div className="flex justify-between">
                  <span>Price:</span>
                  <span className="font-mono">{price} {quoteAsset}</span>
                </div>
              )}
              <div className="flex justify-between font-medium">
                <span>Est. Total:</span>
                <span className="font-mono">
                  {orderType === 'market' ? '~' : ''}{total} {quoteAsset}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderPanel;
