import React, { useState } from 'react';
import { X, TrendingUp, TrendingDown } from 'lucide-react';

interface Position {
  id: string;
  symbol: string;
  side: 'long' | 'short';
  size: number;
  entryPrice: number;
  markPrice: number;
  pnl: number;
  pnlPercent: number;
  margin: number;
  leverage: number;
}

const PositionsPanel: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'positions' | 'orders'>('positions');

  // Mock positions data
  const positions: Position[] = [
    {
      id: '1',
      symbol: 'BTCUSDT',
      side: 'long',
      size: 0.1,
      entryPrice: 44500,
      markPrice: 45200,
      pnl: 70,
      pnlPercent: 1.57,
      margin: 445,
      leverage: 10,
    },
    {
      id: '2',
      symbol: 'ETHUSDT',
      side: 'short',
      size: 2.5,
      entryPrice: 2800,
      markPrice: 2750,
      pnl: 125,
      pnlPercent: 1.79,
      margin: 700,
      leverage: 5,
    },
  ];

  const formatPrice = (price: number) => {
    return price.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const formatPnl = (pnl: number, percent: number) => {
    const sign = pnl >= 0 ? '+' : '';
    return {
      value: `${sign}${formatPrice(pnl)}`,
      percent: `${sign}${percent.toFixed(2)}%`,
      color: pnl >= 0 ? 'text-bull-500' : 'text-bear-500',
    };
  };

  const handleClosePosition = (positionId: string) => {
    console.log('Closing position:', positionId);
    // In real app, this would call the API to close the position
  };

  return (
    <div className="trading-panel h-full flex flex-col">
      {/* Header with Tabs */}
      <div className="trading-panel-header">
        <div className="flex space-x-4">
          <button
            onClick={() => setActiveTab('positions')}
            className={`text-sm font-medium transition-colors ${
              activeTab === 'positions'
                ? 'text-white border-b-2 border-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
          >
            Positions ({positions.length})
          </button>
          <button
            onClick={() => setActiveTab('orders')}
            className={`text-sm font-medium transition-colors ${
              activeTab === 'orders'
                ? 'text-white border-b-2 border-blue-500'
                : 'text-gray-400 hover:text-gray-200'
            }`}
          >
            Open Orders (0)
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'positions' ? (
          <>
            {/* Column Headers */}
            <div className="px-4 py-2 border-b border-dark-700">
              <div className="grid grid-cols-8 gap-2 text-xs text-gray-400 font-medium">
                <div>Symbol</div>
                <div>Side</div>
                <div>Size</div>
                <div>Entry Price</div>
                <div>Mark Price</div>
                <div>PnL</div>
                <div>Margin</div>
                <div>Action</div>
              </div>
            </div>

            {/* Positions List */}
            <div className="flex-1 overflow-y-auto">
              {positions.length === 0 ? (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <div className="text-gray-400 mb-2">📊</div>
                    <p className="text-sm text-gray-400">No open positions</p>
                  </div>
                </div>
              ) : (
                <div className="space-y-0">
                  {positions.map((position) => {
                    const pnlFormatted = formatPnl(position.pnl, position.pnlPercent);
                    
                    return (
                      <div
                        key={position.id}
                        className="grid grid-cols-8 gap-2 py-3 px-4 text-sm hover:bg-dark-700 transition-colors border-b border-dark-800"
                      >
                        {/* Symbol */}
                        <div className="font-medium text-gray-200">
                          {position.symbol}
                        </div>

                        {/* Side */}
                        <div className="flex items-center">
                          <span className={`inline-flex items-center px-2 py-1 rounded text-xs font-medium ${
                            position.side === 'long'
                              ? 'bg-bull-900/30 text-bull-400'
                              : 'bg-bear-900/30 text-bear-400'
                          }`}>
                            {position.side === 'long' ? (
                              <TrendingUp className="w-3 h-3 mr-1" />
                            ) : (
                              <TrendingDown className="w-3 h-3 mr-1" />
                            )}
                            {position.side.toUpperCase()}
                          </span>
                        </div>

                        {/* Size */}
                        <div className="font-mono text-gray-300">
                          {position.size}
                        </div>

                        {/* Entry Price */}
                        <div className="font-mono text-gray-300">
                          ${formatPrice(position.entryPrice)}
                        </div>

                        {/* Mark Price */}
                        <div className="font-mono text-gray-300">
                          ${formatPrice(position.markPrice)}
                        </div>

                        {/* PnL */}
                        <div className={`font-mono ${pnlFormatted.color}`}>
                          <div>{pnlFormatted.value}</div>
                          <div className="text-xs">{pnlFormatted.percent}</div>
                        </div>

                        {/* Margin */}
                        <div className="font-mono text-gray-300">
                          <div>${formatPrice(position.margin)}</div>
                          <div className="text-xs text-gray-400">{position.leverage}x</div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleClosePosition(position.id)}
                            className="p-1 hover:bg-bear-600 text-bear-400 hover:text-white rounded transition-colors"
                            title="Close position"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Summary Footer */}
            {positions.length > 0 && (
              <div className="px-4 py-3 border-t border-dark-700 bg-dark-900">
                <div className="grid grid-cols-3 gap-4 text-xs">
                  <div>
                    <span className="text-gray-400">Total PnL:</span>
                    <div className={`font-mono font-medium ${
                      positions.reduce((sum, p) => sum + p.pnl, 0) >= 0
                        ? 'text-bull-500'
                        : 'text-bear-500'
                    }`}>
                      ${formatPrice(positions.reduce((sum, p) => sum + p.pnl, 0))}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-400">Total Margin:</span>
                    <div className="font-mono text-gray-300">
                      ${formatPrice(positions.reduce((sum, p) => sum + p.margin, 0))}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-400">Positions:</span>
                    <div className="font-mono text-gray-300">
                      {positions.length}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          /* Open Orders Tab */
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 mb-2">📋</div>
              <p className="text-sm text-gray-400">No open orders</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PositionsPanel;
