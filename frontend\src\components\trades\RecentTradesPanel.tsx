import React from 'react';
import { useMarketData } from '../../hooks/useMarketData';

interface RecentTradesPanelProps {
  symbol: string;
}

const RecentTradesPanel: React.FC<RecentTradesPanelProps> = ({ symbol }) => {
  const { recentTrades, loading } = useMarketData(symbol);

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const formatPrice = (price: string) => {
    return parseFloat(price).toFixed(2);
  };

  const formatQuantity = (quantity: string) => {
    return parseFloat(quantity).toFixed(6);
  };

  if (loading) {
    return (
      <div className="trading-panel h-full">
        <div className="trading-panel-header">
          <h3 className="text-sm font-medium">Recent Trades</h3>
        </div>
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="spinner mx-auto mb-2" />
            <p className="text-sm text-gray-400">Loading trades...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="trading-panel h-full flex flex-col">
      {/* Header */}
      <div className="trading-panel-header">
        <h3 className="text-sm font-medium">Recent Trades</h3>
        <div className="text-xs text-gray-400">
          {recentTrades.length} trades
        </div>
      </div>

      {/* Column Headers */}
      <div className="px-4 py-2 border-b border-dark-700">
        <div className="grid grid-cols-3 gap-2 text-xs text-gray-400 font-medium">
          <div className="text-left">Price (USDT)</div>
          <div className="text-right">Amount ({symbol.replace('USDT', '')})</div>
          <div className="text-right">Time</div>
        </div>
      </div>

      {/* Trades List */}
      <div className="flex-1 overflow-y-auto">
        {recentTrades.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <p className="text-sm text-gray-400">No recent trades</p>
          </div>
        ) : (
          <div className="space-y-0">
            {recentTrades.map((trade, index) => (
              <div
                key={`${trade.time}-${index}`}
                className={`grid grid-cols-3 gap-2 py-1 px-4 text-sm font-mono hover:bg-dark-700 transition-colors cursor-pointer ${
                  trade.isBuyerMaker ? 'flash-red' : 'flash-green'
                }`}
              >
                <div className={`text-left ${
                  trade.isBuyerMaker ? 'text-bear-400' : 'text-bull-400'
                }`}>
                  {formatPrice(trade.price)}
                </div>
                <div className="text-right text-gray-300">
                  {formatQuantity(trade.quantity)}
                </div>
                <div className="text-right text-gray-400 text-xs">
                  {formatTime(trade.time)}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer with trade statistics */}
      <div className="px-4 py-2 border-t border-dark-700">
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-400">Buy Volume:</span>
            <div className="text-bull-400 font-mono">
              {recentTrades
                .filter(t => !t.isBuyerMaker)
                .reduce((sum, t) => sum + parseFloat(t.quantity), 0)
                .toFixed(2)}
            </div>
          </div>
          <div>
            <span className="text-gray-400">Sell Volume:</span>
            <div className="text-bear-400 font-mono">
              {recentTrades
                .filter(t => t.isBuyerMaker)
                .reduce((sum, t) => sum + parseFloat(t.quantity), 0)
                .toFixed(2)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RecentTradesPanel;
