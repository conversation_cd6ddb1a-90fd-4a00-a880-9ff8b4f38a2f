import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { toast } from 'react-hot-toast';

interface WebSocketContextType {
  isConnected: boolean;
  subscribe: (stream: string, callback: (data: any) => void) => void;
  unsubscribe: (stream: string) => void;
  sendMessage: (message: any) => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

interface WebSocketProviderProps {
  children: React.ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const wsRef = useRef<WebSocket | null>(null);
  const subscriptionsRef = useRef<Map<string, (data: any) => void>>(new Map());
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = () => {
    try {
      // Close existing connection if any
      if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
        wsRef.current.close();
      }

      // Try different Binance endpoints for better stability
      const endpoints = [
        'wss://stream.binance.com:9443/ws/btcusdt@ticker',
        'wss://stream.binance.com/ws/btcusdt@ticker',
        'wss://data-stream.binance.vision/ws/btcusdt@ticker'
      ];

      const wsUrl = endpoints[reconnectAttempts.current % endpoints.length];
      console.log('Connecting to:', wsUrl, 'Attempt:', reconnectAttempts.current);
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected to Binance');
        setIsConnected(true);
        reconnectAttempts.current = 0;
        
        // Subscribe to default streams
        subscribeToDefaultStreams();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('Received data:', data.e, data.s); // Debug log

          // Handle single stream data (no stream wrapper)
          const streamName = getStreamName(data);
          const callback = subscriptionsRef.current.get(streamName);
          if (callback) {
            callback(data);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected', event.code, event.reason);
        setIsConnected(false);

        // Don't reconnect if it was a manual close
        if (event.code === 1000) return;

        // Attempt to reconnect
        if (reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);

          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Reconnecting... Attempt ${reconnectAttempts.current}`);
            connect();
          }, delay);
        } else {
          toast.error('Connection lost. Please refresh the page.');
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        // Don't show toast on every error to avoid spam
        if (reconnectAttempts.current === 0) {
          toast.error('Connection error occurred');
        }
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      toast.error('Failed to connect to market data');
    }
  };

  const getStreamName = (data: any): string => {
    // Determine stream name based on data structure
    if (data.e === '24hrTicker') return 'ticker';
    if (data.e === 'depthUpdate') return 'depth';
    if (data.e === 'trade') return 'trade';
    return 'unknown';
  };

  const subscribeToDefaultStreams = () => {
    // Binance doesn't require explicit subscription for combined streams
    // The streams are already specified in the URL
  };

  const subscribe = (stream: string, callback: (data: any) => void) => {
    subscriptionsRef.current.set(stream, callback);
  };

  const unsubscribe = (stream: string) => {
    subscriptionsRef.current.delete(stream);
  };

  const sendMessage = (message: any) => {
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    }
  };

  useEffect(() => {
    connect();

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  return (
    <WebSocketContext.Provider value={{
      isConnected,
      subscribe,
      unsubscribe,
      sendMessage
    }}>
      {children}
    </WebSocketContext.Provider>
  );
};
