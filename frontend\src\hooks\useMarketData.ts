import { useState, useEffect } from 'react';
import { useWebSocket } from '../contexts/WebSocketContext';

export interface TickerData {
  symbol: string;
  price: string;
  priceChange: string;
  priceChangePercent: string;
  high: string;
  low: string;
  volume: string;
  quoteVolume: string;
  openTime: number;
  closeTime: number;
}

export interface TradeData {
  symbol: string;
  price: string;
  quantity: string;
  time: number;
  isBuyerMaker: boolean;
}

export interface OrderBookData {
  symbol: string;
  bids: [string, string][];
  asks: [string, string][];
  lastUpdateId: number;
}

export const useMarketData = (symbol: string = 'BTCUSDT') => {
  const { subscribe, unsubscribe, isConnected } = useWebSocket();
  const [currentSymbol, setCurrentSymbol] = useState(symbol);
  const [ticker, setTicker] = useState<TickerData | null>(null);
  const [recentTrades, setRecentTrades] = useState<TradeData[]>([]);
  const [orderBook, setOrderBook] = useState<OrderBookData | null>(null);
  const [loading, setLoading] = useState(true);
  const [useMockData, setUseMockData] = useState(false);

  // Generate mock data when WebSocket fails
  const generateMockData = () => {
    const basePrice = 45000;
    const mockTicker: TickerData = {
      symbol: currentSymbol,
      price: (basePrice + Math.random() * 1000 - 500).toFixed(2),
      priceChange: (Math.random() * 200 - 100).toFixed(2),
      priceChangePercent: (Math.random() * 4 - 2).toFixed(2),
      high: (basePrice + 1000).toFixed(2),
      low: (basePrice - 1000).toFixed(2),
      volume: '1234.56789',
      quoteVolume: '55000000',
      openTime: Date.now() - 86400000,
      closeTime: Date.now(),
    };

    setTicker(mockTicker);
    setLoading(false);

    // Generate mock order book
    const mockOrderBook: OrderBookData = {
      symbol: currentSymbol,
      bids: Array.from({ length: 20 }, (_, i) => [
        (basePrice - i * 10).toFixed(2),
        (Math.random() * 5).toFixed(6)
      ]),
      asks: Array.from({ length: 20 }, (_, i) => [
        (basePrice + i * 10).toFixed(2),
        (Math.random() * 5).toFixed(6)
      ]),
      lastUpdateId: Date.now(),
    };

    setOrderBook(mockOrderBook);
  };

  useEffect(() => {
    if (!isConnected) {
      // Use mock data after 5 seconds of no connection
      const mockTimer = setTimeout(() => {
        console.log('Using mock data due to connection issues');
        setUseMockData(true);
        generateMockData();
      }, 5000);

      return () => clearTimeout(mockTimer);
    }

    if (isConnected && useMockData) {
      setUseMockData(false);
      setTicker(null);
      setRecentTrades([]);
      setOrderBook(null);
      setLoading(true);
    }

    if (!isConnected || useMockData) return;

    // Set up mock data updates if using mock data
    if (useMockData) {
      const mockInterval = setInterval(() => {
        generateMockData();
      }, 2000);

      return () => clearInterval(mockInterval);
    }

    // Subscribe to ticker updates
    subscribe('ticker', (data: any) => {
      if (data.s === currentSymbol) {
        setTicker({
          symbol: data.s,
          price: data.c,
          priceChange: data.P,
          priceChangePercent: data.p,
          high: data.h,
          low: data.l,
          volume: data.v,
          quoteVolume: data.q,
          openTime: data.O,
          closeTime: data.C,
        });
        setLoading(false);
      }
    });

    // Subscribe to trade updates
    subscribe('trade', (data: any) => {
      if (data.s === currentSymbol) {
        const trade: TradeData = {
          symbol: data.s,
          price: data.p,
          quantity: data.q,
          time: data.T,
          isBuyerMaker: data.m,
        };

        setRecentTrades(prev => {
          const newTrades = [trade, ...prev.slice(0, 49)]; // Keep last 50 trades
          return newTrades;
        });
      }
    });

    // Subscribe to order book updates
    subscribe('depth', (data: any) => {
      if (data.s === currentSymbol) {
        setOrderBook(prev => {
          if (!prev) {
            return {
              symbol: data.s,
              bids: data.b || [],
              asks: data.a || [],
              lastUpdateId: data.u,
            };
          }

          // Update existing order book
          const newOrderBook = { ...prev };
          
          // Update bids
          if (data.b) {
            data.b.forEach(([price, quantity]: [string, string]) => {
              const index = newOrderBook.bids.findIndex(([p]) => p === price);
              if (parseFloat(quantity) === 0) {
                // Remove if quantity is 0
                if (index !== -1) {
                  newOrderBook.bids.splice(index, 1);
                }
              } else {
                // Update or add
                if (index !== -1) {
                  newOrderBook.bids[index] = [price, quantity];
                } else {
                  newOrderBook.bids.push([price, quantity]);
                  newOrderBook.bids.sort((a, b) => parseFloat(b[0]) - parseFloat(a[0]));
                }
              }
            });
          }

          // Update asks
          if (data.a) {
            data.a.forEach(([price, quantity]: [string, string]) => {
              const index = newOrderBook.asks.findIndex(([p]) => p === price);
              if (parseFloat(quantity) === 0) {
                // Remove if quantity is 0
                if (index !== -1) {
                  newOrderBook.asks.splice(index, 1);
                }
              } else {
                // Update or add
                if (index !== -1) {
                  newOrderBook.asks[index] = [price, quantity];
                } else {
                  newOrderBook.asks.push([price, quantity]);
                  newOrderBook.asks.sort((a, b) => parseFloat(a[0]) - parseFloat(b[0]));
                }
              }
            });
          }

          newOrderBook.lastUpdateId = data.u;
          return newOrderBook;
        });
      }
    });

    return () => {
      unsubscribe('ticker');
      unsubscribe('trade');
      unsubscribe('depth');
    };
  }, [isConnected, currentSymbol, subscribe, unsubscribe]);

  const changeSymbol = (newSymbol: string) => {
    setCurrentSymbol(newSymbol);
    setTicker(null);
    setRecentTrades([]);
    setOrderBook(null);
    setLoading(true);
  };

  return {
    currentSymbol,
    ticker,
    recentTrades,
    orderBook,
    loading,
    changeSymbol,
    isConnected,
    useMockData,
  };
};
