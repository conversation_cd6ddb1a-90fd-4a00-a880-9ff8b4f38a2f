@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Trading terminal specific styles */
.trading-panel {
  @apply bg-dark-800 border border-dark-700 rounded-lg overflow-hidden;
}

.trading-panel-header {
  @apply bg-dark-900 px-4 py-2 border-b border-dark-700 flex items-center justify-between;
}

.trading-panel-content {
  @apply p-4 h-full overflow-auto;
}

/* Price colors */
.price-up {
  @apply text-bull-500;
}

.price-down {
  @apply text-bear-500;
}

.price-neutral {
  @apply text-gray-400;
}

/* Order book styles */
.orderbook-row {
  @apply grid grid-cols-3 gap-2 py-1 px-2 text-sm font-mono hover:bg-dark-700 transition-colors;
}

.orderbook-bid {
  @apply bg-gradient-to-r from-transparent to-bull-900/20;
}

.orderbook-ask {
  @apply bg-gradient-to-r from-transparent to-bear-900/20;
}

/* Chart container */
.chart-container {
  @apply w-full h-full bg-chart-background;
}

/* Button variants */
.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-success {
  @apply bg-bull-600 hover:bg-bull-700 text-white px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-danger {
  @apply bg-bear-600 hover:bg-bear-700 text-white px-4 py-2 rounded-md font-medium transition-colors;
}

.btn-secondary {
  @apply bg-dark-600 hover:bg-dark-500 text-gray-200 px-4 py-2 rounded-md font-medium transition-colors;
}

/* Input styles */
.input-primary {
  @apply bg-dark-700 border border-dark-600 text-gray-200 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

/* Table styles */
.trading-table {
  @apply w-full text-sm;
}

.trading-table th {
  @apply bg-dark-900 text-gray-400 font-medium py-2 px-3 text-left border-b border-dark-700;
}

.trading-table td {
  @apply py-2 px-3 border-b border-dark-800 font-mono;
}

.trading-table tr:hover {
  @apply bg-dark-700;
}

/* Animation classes */
.flash-green {
  animation: flashGreen 0.3s ease-in-out;
}

.flash-red {
  animation: flashRed 0.3s ease-in-out;
}

@keyframes flashGreen {
  0% { background-color: transparent; }
  50% { background-color: rgba(34, 197, 94, 0.2); }
  100% { background-color: transparent; }
}

@keyframes flashRed {
  0% { background-color: transparent; }
  50% { background-color: rgba(239, 68, 68, 0.2); }
  100% { background-color: transparent; }
}

/* Responsive grid layout */
.terminal-grid {
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  grid-template-rows: 60px 1fr 200px;
  grid-template-areas:
    "header header header"
    "left-panel chart right-panel"
    "bottom-left bottom-center bottom-right";
  gap: 8px;
  height: 100vh;
  padding: 8px;
  background: #0f172a;
}

.header-area { grid-area: header; }
.left-panel { grid-area: left-panel; }
.chart-area { grid-area: chart; }
.right-panel { grid-area: right-panel; }
.bottom-left { grid-area: bottom-left; }
.bottom-center { grid-area: bottom-center; }
.bottom-right { grid-area: bottom-right; }

/* Mobile responsive */
@media (max-width: 1024px) {
  .terminal-grid {
    grid-template-columns: 1fr;
    grid-template-rows: 60px 400px 300px 200px 200px;
    grid-template-areas:
      "header"
      "chart"
      "left-panel"
      "right-panel"
      "bottom-center";
  }
  
  .bottom-left,
  .bottom-right {
    display: none;
  }
}

/* Loading spinner */
.spinner {
  @apply animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500;
}

/* Tooltip styles */
.tooltip {
  @apply absolute z-50 px-2 py-1 text-xs bg-dark-900 text-gray-200 rounded border border-dark-600 pointer-events-none;
}
