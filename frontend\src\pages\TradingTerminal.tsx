import React from 'react';
import Header from '../components/layout/Header';
import ChartPanel from '../components/chart/ChartPanel';
import OrderBookPanel from '../components/orderbook/OrderBookPanel';
import RecentTradesPanel from '../components/trades/RecentTradesPanel';
import OrderPanel from '../components/orders/OrderPanel';
import PositionsPanel from '../components/positions/PositionsPanel';
import OrderHistoryPanel from '../components/orders/OrderHistoryPanel';
import { useMarketData } from '../hooks/useMarketData';
import { useWebSocket } from '../contexts/WebSocketContext';

const TradingTerminal: React.FC = () => {
  const { isConnected } = useWebSocket();
  const { currentSymbol } = useMarketData();

  return (
    <div className="terminal-grid bg-dark-900 text-gray-200">
      {/* Header */}
      <div className="header-area">
        <Header />
      </div>

      {/* Left Panel - Order Book */}
      <div className="left-panel">
        <OrderBookPanel symbol={currentSymbol} />
      </div>

      {/* Main Chart Area */}
      <div className="chart-area">
        <ChartPanel symbol={currentSymbol} />
      </div>

      {/* Right Panel - Recent Trades & Order Form */}
      <div className="right-panel flex flex-col gap-2">
        <div className="flex-1">
          <RecentTradesPanel symbol={currentSymbol} />
        </div>
        <div className="flex-1">
          <OrderPanel symbol={currentSymbol} />
        </div>
      </div>

      {/* Bottom Left - Positions */}
      <div className="bottom-left">
        <PositionsPanel />
      </div>

      {/* Bottom Center - Order History */}
      <div className="bottom-center">
        <OrderHistoryPanel />
      </div>

      {/* Bottom Right - Market Stats */}
      <div className="bottom-right">
        <div className="trading-panel h-full">
          <div className="trading-panel-header">
            <h3 className="text-sm font-medium">Market Stats</h3>
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-bull-500' : 'bg-bear-500'}`} />
          </div>
          <div className="trading-panel-content">
            <div className="space-y-2 text-xs">
              <div className="flex justify-between">
                <span className="text-gray-400">24h Volume:</span>
                <span className="font-mono">1,234,567 BTC</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">24h High:</span>
                <span className="font-mono text-bull-500">$45,678</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">24h Low:</span>
                <span className="font-mono text-bear-500">$43,210</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Open Interest:</span>
                <span className="font-mono">987,654 BTC</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Connection Status Overlay */}
      {!isConnected && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-dark-800 border border-dark-600 rounded-lg p-6 text-center">
            <div className="spinner mx-auto mb-4" />
            <p className="text-gray-300">Connecting to market data...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TradingTerminal;
