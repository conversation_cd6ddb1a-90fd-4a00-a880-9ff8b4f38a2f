{"name": "trading-competition-platform", "version": "1.0.0", "description": "Live trading terminal for competitions with real-time market data and user management", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd backend && npm run build", "build:frontend": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "cd frontend && npm test"}, "keywords": ["trading", "competition", "real-time", "websocket", "react", "nodejs"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^8.2.0"}}